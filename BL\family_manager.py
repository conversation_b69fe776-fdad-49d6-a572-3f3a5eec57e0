import numpy as np
from typing import List, Callable, Dict, Literal, Tuple

from DAL import IDAL, get_dal
from models.enums import Status
from models.family_business import FamilyBusiness
from models.family_member import FamilyMember
from models.role import Role
from utils.logger import get_logger

from .ibl import IBL
from .utils import solve_assignment_problem_maximize


logger = get_logger(__name__)


class FamilyManager(IBL):
    def __init__(self):
        self.dal: IDAL = get_dal()

    # --------------- Family Members Operations --------------- #
    def add_member(self, fm: FamilyMember) -> bool:
        self._validate_fm_change(fm)
        try:
            fm.id = str(self.dal.get_next_id_for_fm())
            return self.dal.add_family_member(fm)
        except Exception:
            logger.error(f'Failed to add member {fm}', exc_info=True)
            return False

    def update_member(self, fm: FamilyMember) -> bool:
        old_fm = self.dal.get_fm_by_id(fm.id)
        if not old_fm:
            raise ValueError(f"The family member {fm.name} wasn't found.")
        if old_fm.status == Status.FAMILY_BUSINESS.value and fm.status == Status.UNEMPLOYED.value:
            if fm.monthly_costs is None:
                raise ValueError(f'make sure to update the monthly costs before removing a member from a staff - {fm}.')
            else:
                fm.personal_monthly_balance = fm.monthly_costs

        self._validate_fm_change(fm)
        try:
            return self.dal.update_family_member(fm)
        except Exception:
            logger.error(f'Failed to add member {fm}', exc_info=True)
            return False

    def get_all_family_members(self) -> List[FamilyMember]:
        return self.dal.get_all_family_members()

    def get_fm_by_id(self, id) -> FamilyMember:
        return self.dal.get_fm_by_id(id)

    def delete_family_member(self, id):
        pass

    # --------------- Family Businesses Operations --------------- #
    def add_family_business(self, fb: FamilyBusiness):
        try:
            fb.id = str(self.dal.get_next_id_for_fb())
            return self._business_op_session(fb, 'add', self.dal.add_family_business)
        except Exception as e:
            logger.error(f'Failed to add business {fb}', exc_info=True)
            raise e

    def update_family_business(self, fb: FamilyBusiness) -> bool:
        try:
            return self._business_op_session(fb, 'update', self.dal.update_family_business)
        except Exception as e:
            logger.error(f'Failed to update business {fb}.', exc_info=True)
            raise e

    def _business_op_session(self, fb: FamilyBusiness, op_type: Literal['add', 'update'],
                             op_callback: Callable[[FamilyBusiness], bool]) -> bool:
        self._validate_fb_change(fb)
        new_staffs = {r.staff_id: r for r in fb.roles}
        new_staffs.pop(None, None)

        if op_type == 'update':
            old_fb = self.dal.get_fb_by_id(fb.id)
            if not old_fb:
                raise ValueError(f"The family business {fb.name} wasn't found.")
            old_staffs = {r.staff_id for r in old_fb.roles}
            old_staffs.discard(None)
            staffs_to_remove = old_staffs - new_staffs.keys()
            for staff_id in staffs_to_remove:
                if not self.dal.set_family_member_status(staff_id, Status.UNEMPLOYED):
                    logger.warning(f'Something went wrong while trying to update unemployed status of {staff_id}, '
                                   f'interrupting process, check the rest of the staff - {staffs_to_remove}')
                    raise TimeoutError(
                        f'Did not succeed to set unemployed status to {self.dal.get_fm_by_id(staff_id).name}, '
                        f'please try again.')

        staff_members_by_id = {m.id: m for m in self.dal.get_all_family_members(fms_ids=list(new_staffs.keys()))}
        for role in new_staffs.values():
            if role.staff_id:
                role.staff_quality = staff_members_by_id[role.staff_id].get_feature_value(role.feature)
            else:
                role.staff_quality = 0

        if not op_callback(fb):
            logger.warning(f'Something went wrong while trying to {op_type} the business {fb}, interrupting process.')
            raise TimeoutError(
                f'Did not succeed to {op_type} the business {fb.name}, please try again.')

        for staff_id, role in new_staffs.items():
            if staff_id not in staff_members_by_id:
                m = f'trying to assign to the role {role.name} in the business {fb.name} a member that do not exists.'
                logger.error(m + f' member id - {staff_id}')
                raise ValueError(m)
            if not self.dal.set_fm_to_role(staff_id, role):
                logger.warning(
                    f'Something went wrong while trying to update new salary and status to new staff {staff_id}, '
                    f'interrupting process, check the rest of the staff - {new_staffs.keys()}')
                raise TimeoutError(
                    f'Did not succeed to update new salary and status to the member '
                    f'{self.dal.get_fm_by_id(staff_id).name}, please try again.')

        return True

    def get_all_family_business(self) -> List[FamilyBusiness]:
        return self.dal.get_all_family_businesses()

    def get_fb_by_id(self, id) -> FamilyBusiness:
        return self.dal.get_fb_by_id(id)

    def delete_family_business(self, id):
        pass

    # --------------- Auxiliary Methods --------------- #
    def _validate_fm_change(self, fm: FamilyMember):
        if fm.status == Status.UNEMPLOYED.value or fm.status == Status.ADOLESCENT.value:
            assert fm.personal_monthly_balance <= 0, (f'The monthly balance of unemployed or adolescent member '
                                                      f'cannot be positive, current state - {fm}')
            fm.monthly_costs = fm.personal_monthly_balance
        elif fm.status == Status.EMPLOYEE.value:
            assert fm.personal_monthly_balance > 0, 'The monthly balance of employed member must be positive.'
        elif fm.status == Status.FAMILY_BUSINESS.value:
            assert next((r for r in self.dal.get_all_roles() if r.staff_id == fm.id), None), \
                f'Did not find any role that is staffed by the family member ({fm.id}, {fm.name}).'

    def _validate_fb_change(self, fb: FamilyBusiness):
        errors = []
        staffs_id = set([r.staff_id for r in self.dal.get_all_roles()]) - set(
            [r.staff_id for r in self.dal.get_fb_by_id(fb.id).roles])

        members = self.get_all_family_members()
        members_id = {m.id for m in members}

        for role in fb.roles:
            if role.staff_id:
                if role.staff_id not in members_id:
                    errors.append(f'The staff id for the role {role.name} was not found.')
                if role.staff_id in staffs_id:
                    errors.append(f'the staffing member for the role {role.name} is already staffing a role.')
                else:
                    staffs_id.add(role.staff_id)

        if errors:
            raise ValueError(
                f'While trying to update the business {fb.name}, something went wrong:\n{'\n'.join(errors)}')

    # --------------- Family Calculations --------------- #
    def advise_staffing(self):
        full_matrix, roles_by_ids, grad_mems_by_ids = self._get_members_to_roles_matrix()
        improvement_matrix = self._transform_members_to_roles_matrix(full_matrix, roles_by_ids.keys(), grad_mems_by_ids.keys())
        print(solve_assignment_problem_maximize(improvement_matrix))
        

        
    
    def _get_members_to_roles_matrix(self):  # -> Dict[Tuple[str, str], Dict[str, Dict[str, float]]], Dict[Tuple[str, str], Role], Dict[str, FamilyMember]:
        fbs_by_id = {fb.id: fb for fb in self.get_all_family_business()}
        businesses = fbs_by_id.values()

        grad_mems_by_ids = {m.id: m for m in self.dal.get_fms_by_status(Status.ADOLESCENT, False)}

        roles_by_ids = {(biz.id, role_id): role for biz in businesses for role_id, role in biz.roles_pairs}

        keyed_matrix = {}
        for r_id, role in roles_by_ids.items():
            keyed_matrix[r_id] = {}
            for m_id in grad_mems_by_ids.keys():
                m = grad_mems_by_ids[m_id]
                base_costs = m.personal_monthly_balance if m.status == Status.EMPLOYEE.value else m.monthly_costs
                salary_for_role = role.evaluate_salary_for_member(m)
                fb_return = fbs_by_id[r_id[0]].get_fb_return_for_role_and_member(r_id[1], m)
                keyed_matrix[r_id][m_id] = {'base_costs': base_costs, 'salary': salary_for_role, 'fb_return': fb_return,
                                      'improvement': salary_for_role + fb_return - base_costs}
        return keyed_matrix, roles_by_ids, grad_mems_by_ids

    def _transform_members_to_roles_matrix(self, keyed_matrix: Dict[Tuple[str, str], Dict[str, Dict[str, float]]], roles_ids: Tuple[Tuple[str, str]],
                                           members_ids: Tuple[str]):
        num_roles = len(roles_ids)
        num_members = len(members_ids)
        if not num_roles or not num_members:
            print("No roles or members available to create assignments.")
            # Stop execution here if there's nothing to assign
            improvement_matrix = np.zeros((1, 1))
        else:
            improvement_matrix = np.zeros((num_roles, num_members))
            for i, r_id in enumerate(roles_ids):
                for j, m_id in enumerate(members_ids):
                    improvement_matrix[i, j] = keyed_matrix[r_id][m_id]['improvement']
        return improvement_matrix


    def validate_db(self):
        for fb in self.get_all_family_business():
            if not self.update_family_business(fb):
                logger.warning(f'Validating the family business {fb.id} failed')
        for fm in self.get_all_family_members():
            if not self.update_member(fm):
                logger.warning(f'Validating the family business {fm.id} failed')

    def calculate_budget(self):
        members = self.dal.get_all_family_members()
        businesses = self.get_all_family_business()
        members_income = sum(m.personal_monthly_balance for m in members)
        business_income = sum(b.evaluate() for b in businesses)
        return members_income + business_income
