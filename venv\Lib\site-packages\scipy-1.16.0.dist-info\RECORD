scipy-1.16.0-cp313-cp313-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.16.0.dist-info/DELVEWHEEL,sha256=cIRju1esVgg_BpbZb0Iy85-H5oo8h8_DrISkF3JBz8I,577
scipy-1.16.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.16.0.dist-info/LICENSE.txt,sha256=Vk1j2-8u8-sd7JSQiWbT7fXxHEh-4s8rr4vz-u8Bvdk,46756
scipy-1.16.0.dist-info/METADATA,sha256=Mo-YBvtgWhUjvYIPB2IkPJMDcyGRKTp2-PnRIUQx5y0,60753
scipy-1.16.0.dist-info/RECORD,,
scipy-1.16.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.16.0.dist-info/WHEEL,sha256=suq8ARrxbiI7iLH3BgK-82uzxQ-4Hm-m8w01oCokrtA,85
scipy.libs/libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll,sha256=ayED8q5NhUeZi10YjpgB-6bLEkBK6OS__zGejMGUkAA,20155392
scipy/__config__.py,sha256=j_W4VtAxSx9otzr-l7PXH9TnIaN5K6pHmfsoUiDUE1c,5607
scipy/__init__.py,sha256=Es2g79F97uihKJdRTUe1KsI1OoJFvmawzBW-jxoERLw,4521
scipy/__pycache__/__config__.cpython-313.pyc,,
scipy/__pycache__/__init__.cpython-313.pyc,,
scipy/__pycache__/_distributor_init.cpython-313.pyc,,
scipy/__pycache__/conftest.cpython-313.pyc,,
scipy/__pycache__/version.cpython-313.pyc,,
scipy/_cyutility.cp313-win_amd64.dll.a,sha256=Iyv28pXlpHPAyXzydIarBDA5NCNM9_yVthKQhrTBOgc,1580
scipy/_cyutility.cp313-win_amd64.pyd,sha256=2A0e5oM71kpVIBdjN-n6kL6URWVPGk2qReFI4Vxetu0,177152
scipy/_distributor_init.py,sha256=CmoJiFF1KyM0MvnPov6kBTO60D43AVlYsfYSmaSuoZY,629
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-313.pyc,,
scipy/_lib/__pycache__/_array_api_compat_vendor.cpython-313.pyc,,
scipy/_lib/__pycache__/_array_api_no_0d.cpython-313.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-313.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-313.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-313.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-313.pyc,,
scipy/_lib/__pycache__/_elementwise_iterative_method.cpython-313.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-313.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-313.pyc,,
scipy/_lib/__pycache__/_sparse.cpython-313.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-313.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-313.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-313.pyc,,
scipy/_lib/__pycache__/_util.cpython-313.pyc,,
scipy/_lib/__pycache__/decorator.cpython-313.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-313.pyc,,
scipy/_lib/__pycache__/doccer.cpython-313.pyc,,
scipy/_lib/__pycache__/uarray.cpython-313.pyc,,
scipy/_lib/_array_api.py,sha256=UnBd9JxR3S8vMm7dQ-0MzxSEEjptcJbyUVq98Ac3i_A,35525
scipy/_lib/_array_api_compat_vendor.py,sha256=119zvLVxjykJaUXHsBRmFIlrE4HWrRg0Q7lwtWayWSI,402
scipy/_lib/_array_api_no_0d.py,sha256=00uQyWlMxKpGlHGZkGzNmbwYGG2vrWjPV_O3j2YExC8,4556
scipy/_lib/_bunch.py,sha256=EOJTn7kAJO2YUw2bdNS1r8ehvh6wUmySxuWKcWZIACg,8534
scipy/_lib/_ccallback.py,sha256=RDz5WUY_jgPtUlEgtm-VGxcub9nvF4laqIsyExK8Nzk,7338
scipy/_lib/_ccallback_c.cp313-win_amd64.dll.a,sha256=0LnNv42xXzJCsXazPZbLEbWBJRrEm2cCdHoAM_B3KJU,1608
scipy/_lib/_ccallback_c.cp313-win_amd64.pyd,sha256=cspBL8JQ01N22no0qyc7zDnJClS9RuuGF2TzL_89yUY,109056
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=CNJ-CCsznIA2OEVZjjqTvPcvp8b3ulUgWBoaQmBLysE,24567
scipy/_lib/_elementwise_iterative_method.py,sha256=HV3KiOA4pFVjbF17qyXPqzFphWQNDiLsG8wVZ6GXTTs,15369
scipy/_lib/_fpumode.cp313-win_amd64.dll.a,sha256=oeHLLdJaCCeufP2tbdLIn-mdSLvbQ5EiUHiSgJLgCl0,1560
scipy/_lib/_fpumode.cp313-win_amd64.pyd,sha256=n5YViiqzT065aqZ-nRA9yQK5nZdcnkbQlizA9nVlalo,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_sparse.py,sha256=eGbe2XbWMCRnJ4xldfs1nP60IcC9ijG5LAjNb-Gs030,916
scipy/_lib/_test_ccallback.cp313-win_amd64.dll.a,sha256=ZZCtZqMGOeyCXD5sMBLKowmey7X7HwzX_CZtXR2N-1Q,1640
scipy/_lib/_test_ccallback.cp313-win_amd64.pyd,sha256=QKBVkzx9eYGhkcmaIexRvJryxwAQ6Xudzatl7b-GkBI,52224
scipy/_lib/_test_deprecation_call.cp313-win_amd64.dll.a,sha256=TCOH_6P2gufLr5kpM3ugcYYrmVVdOE3HATnBE2xPLdQ,1724
scipy/_lib/_test_deprecation_call.cp313-win_amd64.pyd,sha256=eywN6P24A7e00B3SrGHOrlEk2kZiOdD3D55ETr6xCpU,33792
scipy/_lib/_test_deprecation_def.cp313-win_amd64.dll.a,sha256=y24vxJZMD4WsG1Ao7Ldh6SC3QcGL6h6Vld6iHlbpeCE,1712
scipy/_lib/_test_deprecation_def.cp313-win_amd64.pyd,sha256=MSt_qRzHi7PEpey75JfWsmDzJoXymf_tDdHOQfI5dnw,24064
scipy/_lib/_testutils.py,sha256=p4p4BfvylOmKQjw4-BVKvHQzh3QX7Vn9BZ9CIS2q0bM,12652
scipy/_lib/_threadsafety.py,sha256=HflLz5avQPXKV2Dlf-QmHSkzC5PKdbO2JhWp-cZ_Sl4,1511
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=pex73GgY7YUWARVgHzYw_Ky2fdAH7M9BcuDMif_hm38,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-313.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=u9plJrnv8yIMb0zZyCecmJPp_ZIvUCwQ7Y3H-ajGXhs,21238
scipy/_lib/_uarray/_uarray.cp313-win_amd64.dll.a,sha256=mk0Gwn6Vt1pwD4RuWACe5K-h12gmG1CihmnnM3XDBFI,1544
scipy/_lib/_uarray/_uarray.cp313-win_amd64.pyd,sha256=hy8AD4FAA0SeZLRo8ro2uld3WuGoK7ENODcFS75I_7o,235008
scipy/_lib/_util.py,sha256=hmKGZm1TVY-YRgVjzB-zbZ_9pcAPpjXZT2uzeaX6oqE,49948
scipy/_lib/array_api_compat/__init__.py,sha256=0iFT-ou1K6SAhXXusWwTThbx5c4rmKuIcWlRPJoEmg4,1014
scipy/_lib/array_api_compat/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/__pycache__/_internal.cpython-313.pyc,,
scipy/_lib/array_api_compat/_internal.py,sha256=lQwRiR7fcmkl_kqpb8t3cBcwZ6zjvmXGmvMHBK_IVcs,1471
scipy/_lib/array_api_compat/common/__init__.py,sha256=CyVL9VNkuDtuqKghf9EECGKgFTs6N_Mf1hYMw4UZL9E,39
scipy/_lib/array_api_compat/common/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_aliases.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_fft.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_helpers.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_linalg.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_typing.cpython-313.pyc,,
scipy/_lib/array_api_compat/common/_aliases.py,sha256=P1lfcqlT3hwogDPS9bp0gvo4TZLgAjaNnFCVLm_eclk,20371
scipy/_lib/array_api_compat/common/_fft.py,sha256=sRG_SbAJQ2V_i-rAWmJaGfGRu_2EZge3TemYMTTlL8M,4882
scipy/_lib/array_api_compat/common/_helpers.py,sha256=PKesUXa_FAbS_Gv-XM5tQDseoB3rlR62dxf7NHR-e1c,32644
scipy/_lib/array_api_compat/common/_linalg.py,sha256=dKYAjP255C783l17CG0YeDydq3QegLxLcdMiLrWRQ2Q,7088
scipy/_lib/array_api_compat/common/_typing.py,sha256=h9QWpWlR-86bCMiFvxWk7MvdgSkT4IG8n9AxaO1DMKw,4550
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=fO_4V1Dpmm1HxHhCNVd9qQwNmX-l7XA50I_6spRZ4kk,403
scipy/_lib/array_api_compat/cupy/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_aliases.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_info.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_typing.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/fft.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/linalg.cpython-313.pyc,,
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=MSsUWjcO4ch9diPeCCz3f9eJZ8rIeeqzSuuKbGDbBaQ,4998
scipy/_lib/array_api_compat/cupy/_info.py,sha256=DGrFeAcMUDyuI1tFyUnTr9vwVZoOshpexXFtKuZrvhs,10461
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=7sxiaVQnE6sMC_rSUeAxMZYhD-JpUj4NuY6K-o9Uilk,659
scipy/_lib/array_api_compat/cupy/fft.py,sha256=9Uq43Ykr7VXkYooSFkPDAjgMYv8wC_s-FBrVn3ncSFw,878
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=Oc-wZ7mgrnVMNMxGAr0uK6MFTFx6ctzHgYoEEgM2-vo,1493
scipy/_lib/array_api_compat/dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/array_api_compat/dask/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/__init__.py,sha256=24ShJXElbyTN2RX-D-ZJa--yrgR4zg8KmSaMhz-JfQI,332
scipy/_lib/array_api_compat/dask/array/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_aliases.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_info.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/fft.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/linalg.cpython-313.pyc,,
scipy/_lib/array_api_compat/dask/array/_aliases.py,sha256=iGgCNJXSvqL4Qt4Urbgx5zegIpWOyJUj7MED_qgX75Q,11044
scipy/_lib/array_api_compat/dask/array/_info.py,sha256=opbhfe_pEkVnGOykn-SuKq6IsJqBjGEbx7FoaEQ2Nnw,13034
scipy/_lib/array_api_compat/dask/array/fft.py,sha256=RlDvj7DdiJRJp3WAsiBwm00cF8YRSYDIMc0okhi8L-c,610
scipy/_lib/array_api_compat/dask/array/linalg.py,sha256=9_T-QxRmRpQ0NH07CZpSTacPxcQXs1-YsMDe16yrQZM,2523
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=citbnoNp0WL7OCGk2ZVNXSw4V6l4yIvQrsEv97DSUrY,881
scipy/_lib/array_api_compat/numpy/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_aliases.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_info.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_typing.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/fft.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/linalg.cpython-313.pyc,,
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=kyz5F-SKqdgCvHz1lisagweyUckD2uC8ljLNqSKY-eQ,5905
scipy/_lib/array_api_compat/numpy/_info.py,sha256=VtFkU3-BddQBv-lSofjV2BXTZTT4BbGYgq0sgT29DkM,11148
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=di5krexxQm6D3O_LaJzZjBL37R5KWRC6XzJtdwpGx7s,656
scipy/_lib/array_api_compat/numpy/fft.py,sha256=zf7DemaABx5cuEtQqGP8eJHoe0cpPigrc3wL5DyYBt0,814
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=sx9I9cLYHxf_cnBYfyU5GkK_oemELkIaGgvfa66lSl4,4182
scipy/_lib/array_api_compat/torch/__init__.py,sha256=uanOL65J9j2VyVj4HLCOl3HugURGfJ_8lGLbIHIMBhA,571
scipy/_lib/array_api_compat/torch/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_aliases.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_info.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_typing.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/fft.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/linalg.cpython-313.pyc,,
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=CVhn882dV3OpsoXQj_d6lmm4flC8sJYAcQ6-EZevOMc,31116
scipy/_lib/array_api_compat/torch/_info.py,sha256=iqqJe_LwGnl0zJg0_EPcs4TbA1U5jokOdqNNlyBrgfE,12258
scipy/_lib/array_api_compat/torch/_typing.py,sha256=RUDc3VLYNPZ4QcZ5mCD0fArkcfpCFpIgXDwVWnImwzc,111
scipy/_lib/array_api_compat/torch/fft.py,sha256=uBO492XGK5JxvwWa0BaLUxHUfHOV2xnrAUeTkBP_EMA,1823
scipy/_lib/array_api_compat/torch/linalg.py,sha256=BFarWaUhnIbvR4vZOaYTaxu7ccimBe_DsNI0g3tIRRU,4920
scipy/_lib/array_api_extra/__init__.py,sha256=0oWJtXcpMbixqgf2YXfAGYLyzLdvqgPbWtUtDSbBCOY,703
scipy/_lib/array_api_extra/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_extra/__pycache__/_delegation.cpython-313.pyc,,
scipy/_lib/array_api_extra/__pycache__/testing.cpython-313.pyc,,
scipy/_lib/array_api_extra/_delegation.py,sha256=g3YtEGWirCIowEVWL2W0ybMvISXfJ2kwdtpRjZrvCoE,6282
scipy/_lib/array_api_extra/_lib/__init__.py,sha256=54Nmk8Es53HAt1-rXseopE_HoscrG422fOJh9L6GoVY,37
scipy/_lib/array_api_extra/_lib/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_at.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_backends.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_funcs.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_lazy.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/__pycache__/_testing.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/_at.py,sha256=bLbhTdrck3iFKgW2Fiwg_xmWD-N3qnJx2UzShrQN0Qg,15825
scipy/_lib/array_api_extra/_lib/_backends.py,sha256=bu75dpSHfbshnQQ5W04_W47K1dY1CbIb-OYIMI5msT0,1514
scipy/_lib/array_api_extra/_lib/_funcs.py,sha256=izeqFPdaEQ1dDW9FGj_8gzJn5Yuu71Ys_TOcjDwqn7Q,30708
scipy/_lib/array_api_extra/_lib/_lazy.py,sha256=16R8n7RCPbAZdEOC-wmW2C6V7QHPDHz3J_bP0Foxfxc,14291
scipy/_lib/array_api_extra/_lib/_testing.py,sha256=IIw-2T0j8OPE04HSabiCeZQavULrV6-bBvnuhDF0oto,9448
scipy/_lib/array_api_extra/_lib/_utils/__init__.py,sha256=X4P4_BL9SID6Iu2QTN8ITK0e2JmUZcB2zgc5IUpHwbk,50
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_compat.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_helpers.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/__pycache__/_typing.cpython-313.pyc,,
scipy/_lib/array_api_extra/_lib/_utils/_compat.py,sha256=YCUEqXGbybJktoSEGVGvDWwxdEjLM4mXlax-t0-5t9Q,1886
scipy/_lib/array_api_extra/_lib/_utils/_compat.pyi,sha256=OjFbBkm4zVNDSoRMfErknxQNMYKyEt31ARkjQYo8wao,1795
scipy/_lib/array_api_extra/_lib/_utils/_helpers.py,sha256=N0pkp_pITG0SUcbUD38uCVm_2YwnpU72vWV4UtEqNEs,18068
scipy/_lib/array_api_extra/_lib/_utils/_typing.py,sha256=-OAglip1djeAGAffDhviAfeDef55WAu8r6q8FuShz7M,238
scipy/_lib/array_api_extra/_lib/_utils/_typing.pyi,sha256=UDP4eCXVo01YaUls--mWN58sgY_6cIcWzoe3vPu2bgo,4830
scipy/_lib/array_api_extra/testing.py,sha256=XfNf_Wky_Xvet9SnBfxzpv3c5htQwbXAG0vhFR-jbNU,13267
scipy/_lib/cobyqa/__init__.py,sha256=0JG2qoARCnrJ4xv4L1fvfEHXSI4_IYJPtIT5UbAtvAk,598
scipy/_lib/cobyqa/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/cobyqa/__pycache__/framework.cpython-313.pyc,,
scipy/_lib/cobyqa/__pycache__/main.cpython-313.pyc,,
scipy/_lib/cobyqa/__pycache__/models.cpython-313.pyc,,
scipy/_lib/cobyqa/__pycache__/problem.cpython-313.pyc,,
scipy/_lib/cobyqa/__pycache__/settings.cpython-313.pyc,,
scipy/_lib/cobyqa/framework.py,sha256=nIzsKelKL8-xa_QMv8QKTrbdvSsk5fLRmCOOx6YyhXg,40140
scipy/_lib/cobyqa/main.py,sha256=HXihzfF2vCRGUHsBgxBJOzY7F7ecFmrDqYs2HwzaErQ,59033
scipy/_lib/cobyqa/models.py,sha256=XPQj8dX2f_5tJHGpn7siHmrJ6Zp8Y2-3gK54RiYvWjw,52185
scipy/_lib/cobyqa/problem.py,sha256=rZyUJqIJcsuAkejQK_xJMEPfkMjOE8uHk1X_lB6mb8A,41499
scipy/_lib/cobyqa/settings.py,sha256=fWu0NPyFiBE-8No4R9B4pUKSjEbuSlaa0AWtHIzHblA,3958
scipy/_lib/cobyqa/subsolvers/__init__.py,sha256=96yleneJh-YtMtCa8tG8cfsNM-Y_1lF30jJ5tnIKIQg,355
scipy/_lib/cobyqa/subsolvers/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/geometry.cpython-313.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/optim.cpython-313.pyc,,
scipy/_lib/cobyqa/subsolvers/geometry.py,sha256=1jO30o2Nm4BiKpb-GAv8Uqj1SnDNLpo9J7ECoOUdP3o,14560
scipy/_lib/cobyqa/subsolvers/optim.py,sha256=-tJVfZcS66vfQYri293E4Bx710VqYT-N00bYN6vpo9I,46715
scipy/_lib/cobyqa/utils/__init__.py,sha256=dyc27b5M6bGiH7f1laeaoqaQeEKlKvFevVghBV7LrtY,377
scipy/_lib/cobyqa/utils/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/exceptions.cpython-313.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/math.cpython-313.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/versions.cpython-313.pyc,,
scipy/_lib/cobyqa/utils/exceptions.py,sha256=rFMjALTKz9xNnb4pTS5v_VIXqh0T7QpwlkK8YUeN000,505
scipy/_lib/cobyqa/utils/math.py,sha256=QhsfzOYAJnLtViuMAXKNQwpRd5ZKMNs_faeJ3TKQdnw,1688
scipy/_lib/cobyqa/utils/versions.py,sha256=S2JG4-zryLBoBf2yzVlIRs1sW6eMUeWFfcjWn2D39jg,1532
scipy/_lib/decorator.py,sha256=cxk2B6E6i73n4Kb2SfNvCXhpXCCSfgWeQRhe5_h482c,15413
scipy/_lib/deprecation.py,sha256=Sk3aEvBPTml1uJVvM--nQqhB2GG82LjnYhDmZWDnN0w,10114
scipy/_lib/doccer.py,sha256=pH26P3umqqBg9onVmrn4mOuYCnEEXPelouYTPIxUtaw,11087
scipy/_lib/messagestream.cp313-win_amd64.dll.a,sha256=Tj0qsB9Z_8YTRMHGN-kkDoT3xvt9qfewJBDVy4vkurc,1616
scipy/_lib/messagestream.cp313-win_amd64.pyd,sha256=xdaV1LWuppqWwa9W7SBXjwUAPkZc6cYwotQSIrkSwZA,63488
scipy/_lib/pyprima/__init__.py,sha256=bIGLCy0FYi-8YrB4BQ0jWXAz_D4xb9Yy5EAEShmefOU,8673
scipy/_lib/pyprima/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/pyprima/cobyla/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/cobyla.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/cobylb.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/geometry.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/initialize.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/trustregion.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/__pycache__/update.cpython-313.pyc,,
scipy/_lib/pyprima/cobyla/cobyla.py,sha256=1lqxU46SAfOy59wmF4PtbHbY1emigYhawLGffUYanYM,22705
scipy/_lib/pyprima/cobyla/cobylb.py,sha256=PWp_pbM93fT0tyeJ7MlCgHdq34spp2ida8ZfzAjniC0,41478
scipy/_lib/pyprima/cobyla/geometry.py,sha256=PCA-yJvVOv__lOyZLzLtFnBcUtPivmS1dUtGB7M4EV4,10871
scipy/_lib/pyprima/cobyla/initialize.py,sha256=90HbJD3fs3H0Kad59KdbBnUdB1aoIMLtqCEboR3epOU,9624
scipy/_lib/pyprima/cobyla/trustregion.py,sha256=5ymq1eLJKQukyx4mZJ4-PABZwAd-NKzAVRDtomFdna8,25692
scipy/_lib/pyprima/cobyla/update.py,sha256=USsK6gPaTHp79oqGNoLXiK4mwA97QIIGG7X9KHgd_ck,14171
scipy/_lib/pyprima/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/pyprima/common/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/_bounds.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/_linear_constraints.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/_nonlinear_constraints.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/_project.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/checkbreak.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/consts.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/evaluate.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/history.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/infos.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/linalg.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/message.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/powalg.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/preproc.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/present.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/ratio.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/redrho.cpython-313.pyc,,
scipy/_lib/pyprima/common/__pycache__/selectx.cpython-313.pyc,,
scipy/_lib/pyprima/common/_bounds.py,sha256=g30OwFpRJDtlPMEmedgN7E3V0J_3BJVoKs2Dks9y_M0,1670
scipy/_lib/pyprima/common/_linear_constraints.py,sha256=Rw4Tf4k7Qun1yUI_6FKjEvdIRUldU3mME7JWYJvNw-w,2208
scipy/_lib/pyprima/common/_nonlinear_constraints.py,sha256=NMJq3xPkXxeD_zeIpdrYtBrstbSSAoWKvJuFwJRp5dQ,2204
scipy/_lib/pyprima/common/_project.py,sha256=Uf0BpAWF-rqFuI-xZl6s6-lLdvJfDhX7A5y1jVCn2mc,8036
scipy/_lib/pyprima/common/checkbreak.py,sha256=d56n4A0IgR22l1zkigGZNDYo0h5vrmagQlpROmSWX48,3544
scipy/_lib/pyprima/common/consts.py,sha256=J_CC0IQZ5yiPp01ivf05LpUF9Ma5rMmJRMYRVcDZyWE,1357
scipy/_lib/pyprima/common/evaluate.py,sha256=aB88uTuA051CbnLQySDiqt2IprH9qRFE0trnvgdzaJs,3249
scipy/_lib/pyprima/common/history.py,sha256=bt12OZu7i3_hmJw7YmKR6HZzmPTiC_wPm5WutHlVRaM,1399
scipy/_lib/pyprima/common/infos.py,sha256=wkHFBpcqp1ek0MAUVYWAHb2iXmaYj7gCiKEHgF6krXY,734
scipy/_lib/pyprima/common/linalg.py,sha256=gD630D_5MT9b2pqp0rN_53PqET1TrAWnhNkDJr8ItLE,14715
scipy/_lib/pyprima/common/message.py,sha256=Aaip8pKVyTawMfn4EqFMKOOgBgDKc40z96dQD1NooAs,9953
scipy/_lib/pyprima/common/powalg.py,sha256=2FvikNkrqnRiOiEFWIVdaMqvVNgGrzjSpycKakhnjps,6916
scipy/_lib/pyprima/common/preproc.py,sha256=HUWvXC4fJTuhI1-mlfU9W13Pi5ExN0x_hFCPLYW5eaM,14097
scipy/_lib/pyprima/common/present.py,sha256=qe3bW4i51R75RijS5fW9-w1RT7CVytrwB8lPD_9Mn6E,150
scipy/_lib/pyprima/common/ratio.py,sha256=HLM_fTDciUQOzFnNpPGe49H655-5ceRKUnKs94BOs9E,1877
scipy/_lib/pyprima/common/redrho.py,sha256=dozBiV1pIOLrgSCO9CplOiPyP6NghQhOgjZ3meoWpHU,1305
scipy/_lib/pyprima/common/selectx.py,sha256=72XIS-jDv_-mjLFKYePm4Equ4XsGDS1wm4VMtgr4ZkU,14416
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_config.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_doccer.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-313.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-313.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=vdtwdwiItw_VvyM1NTdbT0G4BxxEBFdTWQ7K67NFuzQ,3839
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=fugqfo5OTXKU55s8Au00phTnZvHgUUnUNeyYU8OFfS4,24563
scipy/_lib/tests/test_array_api.py,sha256=XD8gurDpOfkIwkbikrg8hYCZE-JMk-ZOiU21tbniBP4,13705
scipy/_lib/tests/test_bunch.py,sha256=Ozcm5j5VSBOuQZOf-Qaq0XeIEIDpHeyDcbR5KnwPyBc,6558
scipy/_lib/tests/test_ccallback.py,sha256=CiOVcS1i6rYSo86a9pIrVsdIXjTDyHVAclpaaEElB1g,6236
scipy/_lib/tests/test_config.py,sha256=gk6SUpFQ8CIJ5mrnn2wino0IPryTF2PM2M891PG5Z74,1320
scipy/_lib/tests/test_deprecation.py,sha256=dii6UZ3ZiUJ0Lu4ubX5SkC4R9NkwBireWJOP5b9qJSA,400
scipy/_lib/tests/test_doccer.py,sha256=wmoKV2T7y_6K0P2RtN8d6IvxXCvuctzQkjgmVgRMSnc,4196
scipy/_lib/tests/test_import_cycles.py,sha256=9PxfWvQUdWHRPxjp74H-F8AAgb7XL4niOUfGusfVL3E,604
scipy/_lib/tests/test_public_api.py,sha256=h-SsYoHKK1ZXAD-dQSyChyCBDHaC0pmjH6g6bOAc9iQ,19282
scipy/_lib/tests/test_scipy_version.py,sha256=R_wW_ajgGbFq0cl3N88Msg9bYUu6_qJPHOywArtT-XA,946
scipy/_lib/tests/test_tmpdirs.py,sha256=wNCRS8MSRZZPe-2g4U_eCUHGYF6uA9SBsH6pzAARnec,1385
scipy/_lib/tests/test_warnings.py,sha256=9v2UdRO6GQxHfUzubef-AOaTGgDl8DaBQIRFhW4Ul_Q,5086
scipy/_lib/uarray.py,sha256=qXvvUluJiQJwLybyo5ZZtGGWa_o2T0mSvojeu8t_IkQ,846
scipy/cluster/__init__.py,sha256=AWNDaf2OiXXDbfg_d9SqDxhV7DE6o36K3u0zw5O7jXA,911
scipy/cluster/__pycache__/__init__.cpython-313.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-313.pyc,,
scipy/cluster/__pycache__/vq.cpython-313.pyc,,
scipy/cluster/_hierarchy.cp313-win_amd64.dll.a,sha256=0iT_Ju1Y9TUG1j4tXB3KKUzdTZbzx9Kbw1W3a_Hk-04,1580
scipy/cluster/_hierarchy.cp313-win_amd64.pyd,sha256=fHnqSFYmxLXUPmjrBNmhzi1oRcCkT9VZdLu5nC9yu3Y,274432
scipy/cluster/_optimal_leaf_ordering.cp313-win_amd64.dll.a,sha256=XT1E7rrUwxHSCqygtweubP9GS22Z9HCSsFyvXl3DljA,1724
scipy/cluster/_optimal_leaf_ordering.cp313-win_amd64.pyd,sha256=WKYAb5cloY6xVeDjshQ4i8vF6yJ7HvyFRWHzZbiaVsg,188928
scipy/cluster/_vq.cp313-win_amd64.dll.a,sha256=q8InXXykIDV-xY3VjEYEx6CP3fVdC7Opk1TbfU_fDg8,1496
scipy/cluster/_vq.cp313-win_amd64.pyd,sha256=f2c2eo_cMcnH-8W_CQYm_DgKK8pjCmxrZ0wo-mDH3mI,105472
scipy/cluster/hierarchy.py,sha256=G-dS6sf_Ku4olA7jiPRNLb-O7A7pElwLo6GxyZgS0hQ,161159
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-313.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-313.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-313.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-313.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=k6gXaC2uXVsunUv1xWPgmRWcYFCDucjGkvYgVYN-CO0,51520
scipy/cluster/tests/test_vq.py,sha256=biI59apSPU--lGcHEOlZFztiqstRH-jtDQjzmHnri7o,18646
scipy/cluster/vq.py,sha256=2S7XIpG4fOa2o62xtMLcClxDEHOoOSRegPboDOx61a4,31731
scipy/conftest.py,sha256=yb88-cD1vL0sxHSqkiXkXYcgzlu-qtO-WjZCKdBlVg8,28185
scipy/constants/__init__.py,sha256=dL-gJZdTq_191I1mLrl6SoSvi_Ewk39gcJGylOG3fVo,15197
scipy/constants/__pycache__/__init__.cpython-313.pyc,,
scipy/constants/__pycache__/_codata.cpython-313.pyc,,
scipy/constants/__pycache__/_constants.cpython-313.pyc,,
scipy/constants/__pycache__/codata.cpython-313.pyc,,
scipy/constants/__pycache__/constants.cpython-313.pyc,,
scipy/constants/_codata.py,sha256=ZyC5LwdzQT2durBSoqND6andYDLo8M2RbJZ_3UVT0wM,204620
scipy/constants/_constants.py,sha256=IAZt-vJi8n9lmOrrS0oLiG9Tm3_Oqp47o7cO0txDiQE,10940
scipy/constants/codata.py,sha256=Fayz-HkpNqk8aOadBz7AfFpKinS4JcYhKBm_JUweY-o,635
scipy/constants/constants.py,sha256=vdC_jDTQZWQlrkYZY0Uj7NKO2AtBwH5mc28Jhy7Ur9s,2303
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-313.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-313.pyc,,
scipy/constants/tests/test_codata.py,sha256=q4nr2_IdPyNT0VWIGPTPfs4Vyr6bRohI5ZfLolT2pFs,2932
scipy/constants/tests/test_constants.py,sha256=BpSQTngKgDE1USsXs22mR29eWcp41u1dK5un83zZ6Xw,4330
scipy/datasets/__init__.py,sha256=EUFFXPZhchasAFFWACy12Sz1A6w3wgUXBICgfTmAaHE,2892
scipy/datasets/__pycache__/__init__.cpython-313.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-313.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-313.pyc,,
scipy/datasets/__pycache__/_registry.cpython-313.pyc,,
scipy/datasets/__pycache__/_utils.cpython-313.pyc,,
scipy/datasets/_download_all.py,sha256=uy1-bi9BHpyZOTjoxbgkbVTjgem-dvHs-lPjuwnf4j4,2166
scipy/datasets/_fetchers.py,sha256=PixEOlBBc7j1DWZIuMWZna3n15LmWnCBSry4oM8xDFw,7166
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=vc9cQUWxh81rSYuaI_I5jiseJV7B-UqYAVfBIz0ztvs,3047
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-313.pyc,,
scipy/datasets/tests/test_data.py,sha256=op7cichKh15h3N0uSyJ1h3Mt-5q_ipZrIVILJjlZpmw,4341
scipy/differentiate/__init__.py,sha256=s67FqxVRyuL-VKPjOxeahlOLVlHCwHp5f_6xEmGmHsg,648
scipy/differentiate/__pycache__/__init__.cpython-313.pyc,,
scipy/differentiate/__pycache__/_differentiate.cpython-313.pyc,,
scipy/differentiate/_differentiate.py,sha256=QTcfpKrBxlda2hVNm47g_rZueuzTdZRDaj4ePxyWJq8,51944
scipy/differentiate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/differentiate/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/differentiate/tests/__pycache__/test_differentiate.cpython-313.pyc,,
scipy/differentiate/tests/test_differentiate.py,sha256=yj7tHj0UWGdHMNqV0DEGIx0yzYtIOHU7pEzmKjwe6Vg,28847
scipy/fft/__init__.py,sha256=Sn1qqeuX6MkMiU7eRyFMEE3ekkecR5Sht-WK-dWaPvk,3746
scipy/fft/__pycache__/__init__.cpython-313.pyc,,
scipy/fft/__pycache__/_backend.cpython-313.pyc,,
scipy/fft/__pycache__/_basic.cpython-313.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-313.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-313.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-313.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-313.pyc,,
scipy/fft/__pycache__/_helper.cpython-313.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-313.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-313.pyc,,
scipy/fft/_backend.py,sha256=ySd0v6IodaTh-_kjfEOogdlfCsJ3rdcRLqv-ZU8NkTM,6740
scipy/fft/_basic.py,sha256=0vwIrMp0-EoTqn6UOuTbwDn6-TNUDZ7JIHow_y3KqPI,65196
scipy/fft/_basic_backend.py,sha256=eLhjbNBFsM40DtuTtofDo_F20l8kZ5vuZbfB5aQ1rVk,7644
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=NnJ5-3iwtWwapt-yLhI9ySLqB_cOJvOnxOuDm8XJQa0,8087
scipy/fft/_fftlog_backend.py,sha256=tXS8zJyOD1aQeGpT2ac_7l8q6gmQ3brLm7uphdeYB14,5504
scipy/fft/_helper.py,sha256=_AU0XP-BRXEHYKIPWEUzLhV5pDHy4EC5RUxgWrk_rwY,11609
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-313.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-313.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-313.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-313.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=dST8PBFhoiOR1Kj1j3CcjMC0p7KFxTA9qDr1oN2YrFw,8389
scipy/fft/_pocketfft/helper.py,sha256=Y3l07DPcHyVv2o3mwdHJHrNBBLPQiOWyTCBvq01c6aI,7086
scipy/fft/_pocketfft/pypocketfft.cp313-win_amd64.dll.a,sha256=-BBMLDX-6ESCq1weKOc52eqTFvnIFOuXcVfLDeObOlY,1592
scipy/fft/_pocketfft/pypocketfft.cp313-win_amd64.pyd,sha256=1CYTPR3qEZhAXB4d1DbaFSlhtt7QX6Hs7MFDbpLw7dU,1079296
scipy/fft/_pocketfft/realtransforms.py,sha256=nk1e31laxa9920AeBgZgb1vZSXIpoILwWqSI028HCyQ,3453
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-313.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-313.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=pGMcpRVdqgEAnNvw7lkLIb3Dwaz8-xjc0lt2JjZUoME,36644
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=3_jRiEXqmJd5JS70Or6u244xN0Cw2YU6Vfmqd0PV6RA,17368
scipy/fft/_realtransforms.py,sha256=ou9uQqHBe9tWCFYK378ZVUskm7z0rBa2Pcb-_ddxRko,26486
scipy/fft/_realtransforms_backend.py,sha256=hJ3LkFOvggibVF_E36fM9R3ZqAJHHicYrGUceH8B81g,2452
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-313.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-313.pyc,,
scipy/fft/tests/mock_backend.py,sha256=mnU42TLolLB-fNi3tzxY_QFK9sl4f1dNswNNr6QwpIE,2781
scipy/fft/tests/test_backend.py,sha256=wqI0cuYwwEj_8eTFDjqoOXVioCAExf1YvJ6VmhsFU9c,4383
scipy/fft/tests/test_basic.py,sha256=gHqZ-EvkSjXe_Lngrc0S_Duwt_xGWmnDT3zcmqee4rw,20919
scipy/fft/tests/test_fftlog.py,sha256=ZvIZ603JHwxURK3EMMmjVCgqYaC9J8h2JCsdlDDnopM,7858
scipy/fft/tests/test_helper.py,sha256=Dm8lWAcBsb7mYzBr8wXwUoU1c6rUT71DmrFswor3Ync,20080
scipy/fft/tests/test_multithreading.py,sha256=hMqYEawnqaoCS3fp6dOSLBn1l4OR6D7txckjod8vipc,2234
scipy/fft/tests/test_real_transforms.py,sha256=TnFV8Z6wOsQpB0n4aEfHCuYmsrJLGCtn3jVhgBEHF3Y,9413
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/__pycache__/__init__.cpython-313.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-313.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-313.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-313.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-313.pyc,,
scipy/fftpack/__pycache__/basic.cpython-313.pyc,,
scipy/fftpack/__pycache__/helper.cpython-313.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-313.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-313.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=krb3yRwgVmF-r3t-n-FGyNa8h5qhfiP4p0UGxQ39BNo,3463
scipy/fftpack/_pseudo_diffs.py,sha256=MIvGWp5wHl1ykkBheub658tnDjl6_76EEx-aM7o11XM,16490
scipy/fftpack/_realtransforms.py,sha256=RT4ScAxB-Em_Dd8j2Ca7VKQfUSOHpwrYADrjKQXBifk,19820
scipy/fftpack/basic.py,sha256=FFn2KxrsmC6IsOQdjcoVr8Nvrlng2FRiv7gNeT1ZrY4,597
scipy/fftpack/convolve.cp313-win_amd64.dll.a,sha256=PLw54O9kKHf2EZyE6IV4iOnnog2zgiCodksG6fHw1yc,1560
scipy/fftpack/convolve.cp313-win_amd64.pyd,sha256=CrzjmEb8618elDXI2d0m72beeCcTup-6cTTErJHsuGA,124416
scipy/fftpack/helper.py,sha256=1b1b278FWyTc2MeAjeLFB8eyV76pRxOigGtBUvCp_lo,599
scipy/fftpack/pseudo_diffs.py,sha256=oLCcXufpR_wAj4TKj3OTqyIEGXag1xSl_ueO83XspMI,680
scipy/fftpack/realtransforms.py,sha256=oUJXNb5KAyS4k8xubnE7hGE9BpLCcdkk_iiReyB8OOE,614
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-313.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-313.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-313.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-313.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-313.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=ZO3lmZu3NDj-uVspQB-GUIPozO3m0DBUjJ5nj4lAjew,31349
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=v63sC7YHLiOcaMWZ75_t0Nasz2Q8Tq-lo7awD4CINwI,1189
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=2NGiR5OlUGn6ybqbzLD1WXTltWpfBuv3e0EW8xk1cBU,14121
scipy/fftpack/tests/test_real_transforms.py,sha256=CHVhhQvZ5_Aff9wOANN5MFuFKdW5muPCLa2c9XS-AYg,25296
scipy/integrate/__init__.py,sha256=RIEI7skWhbt4SVbMlV4BnP4EEOAY8AVy-iJ2K-ovp44,4495
scipy/integrate/__pycache__/__init__.cpython-313.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-313.pyc,,
scipy/integrate/__pycache__/_cubature.cpython-313.pyc,,
scipy/integrate/__pycache__/_lebedev.cpython-313.pyc,,
scipy/integrate/__pycache__/_ode.cpython-313.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-313.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-313.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-313.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-313.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-313.pyc,,
scipy/integrate/__pycache__/dop.cpython-313.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-313.pyc,,
scipy/integrate/__pycache__/odepack.cpython-313.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-313.pyc,,
scipy/integrate/__pycache__/vode.cpython-313.pyc,,
scipy/integrate/_bvp.py,sha256=zBE48hiT8ryC7pqrQcyr7ZuPss37Ixq5gVF17tz2ofo,42373
scipy/integrate/_cubature.py,sha256=0z_Wqx1Viwwaa0r1RaLfWW2MqQJhmj7XcTu_YfGpcU0,26434
scipy/integrate/_dop.cp313-win_amd64.dll.a,sha256=PdA7h1chUmew-5i0uphIbrqunJ7_pCff78Ub8J4Trf4,1512
scipy/integrate/_dop.cp313-win_amd64.pyd,sha256=91h8gwqYXrlWcAD1XUclpTY9HvZezrxrZRltoXbUUHI,435200
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-313.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-313.pyc,,
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=i_XH2ClhYxdjZuunYZX5h7HfqLbNQWib2ep8_KrM3Q0,17979
scipy/integrate/_ivp/common.py,sha256=ad9h8tAf2fzoxqtvSYE3u4kXdaf381sl0h7vJztFvqc,16196
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=FF2eM8zxQ_xOk5j1CBQWs98eSDgdHFtdcZVn71gLpCU,32498
scipy/integrate/_ivp/lsoda.py,sha256=95NTm0zyHR5zFH5IeKo9kEWtDkdR3Mdfrbubnsf4Awo,10151
scipy/integrate/_ivp/radau.py,sha256=VoxSVlIU3osykkQ_YXv3BSXcQ24hMX8-RiKgtfHcA0E,20248
scipy/integrate/_ivp/rk.py,sha256=F8juhimgqsq5y6-9kpT_BcFqGOhv8PaD__LZcVSonr8,23401
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-313.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-313.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=TROJj-ueTuEMBzneCLSftR4JjKcE33EL3MDMUQywCtE,44110
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lebedev.py,sha256=QfCd7zmEI4jJjPGBvM6hMU55gk7nENOElNSxFdYWJaU,267474
scipy/integrate/_lsoda.cp313-win_amd64.dll.a,sha256=39IoLNPQGT6aeI1HMBVfNNglcffIMBqDThoiBxKqkNw,1532
scipy/integrate/_lsoda.cp313-win_amd64.pyd,sha256=6llC4bRmTv1GuxVHUr99-e3F6IwmLBNqOr91H0SUVig,556032
scipy/integrate/_ode.py,sha256=0v5na3NcP99umMIWxmsKCYYdnFGBVlJjlt_cKb38CVk,50017
scipy/integrate/_odepack.cp313-win_amd64.dll.a,sha256=aazwq2WzNzyESpxk2hm83pW6_wvQfR47u10gnQcSM4A,1560
scipy/integrate/_odepack.cp313-win_amd64.pyd,sha256=CPXmkiSQ9zxwb6sf137Gf8fKCnscue8lJknTiYMZXFk,534528
scipy/integrate/_odepack_py.py,sha256=HkZMTdAlbnO2-ti2gQ3EracLPuUoygvRE4A5z8uoxag,11504
scipy/integrate/_quad_vec.py,sha256=MqtKY4oCL0VUnoLxfRaLM8z8giDpG13Rz7C6r5um0P8,22256
scipy/integrate/_quadpack.cp313-win_amd64.dll.a,sha256=ZRk93xLDLCP7E7I6dku530BYfmUGJ2UT4etYVep7tp8,1568
scipy/integrate/_quadpack.cp313-win_amd64.pyd,sha256=OPUlgLOO3_dlKXrZD5pFEdaNetbuCVp3tyarzct07qY,143360
scipy/integrate/_quadpack_py.py,sha256=QO2I4Mx0JIjzuT0SXG76G64m7WYptbGurNS5WynmJ3U,54987
scipy/integrate/_quadrature.py,sha256=Xr27yVAoOGmELn3AKMg5nm06sW8Jec-UHSRew7U08E8,49215
scipy/integrate/_rules/__init__.py,sha256=HFHYDYs5UOQljteKeDXGkSFb-T9ehI3N3gFWE8QK6vQ,340
scipy/integrate/_rules/__pycache__/__init__.cpython-313.pyc,,
scipy/integrate/_rules/__pycache__/_base.cpython-313.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_kronrod.cpython-313.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_legendre.cpython-313.pyc,,
scipy/integrate/_rules/__pycache__/_genz_malik.cpython-313.pyc,,
scipy/integrate/_rules/_base.py,sha256=CAAHyVIRcByCsb5P-LkvOCurHq7McnmwunLiqy3mRYk,18445
scipy/integrate/_rules/_gauss_kronrod.py,sha256=tJ5h1xaYEdiJ_5wCQsKCvEaXB0yjAl9luI78rg9vXxM,8675
scipy/integrate/_rules/_gauss_legendre.py,sha256=J3mKoTtZlvxUdHnFYw2dPSsWAzdVXovV3f7UQnT5BT0,1795
scipy/integrate/_rules/_genz_malik.py,sha256=dqe9DLj0DhNttRn4CTtp0f41sZb8b1uBNSM7CeBXrJo,7518
scipy/integrate/_tanhsinh.py,sha256=fVlosHVtTLwuovVjLkDI1q-3SjuiCFclpLveF-LM_Wc,62724
scipy/integrate/_test_multivariate.cp313-win_amd64.dll.a,sha256=HpuG4eWKzeIW7moTp1Xn7aQkv3MqZC_aFDbOxz4c3As,1676
scipy/integrate/_test_multivariate.cp313-win_amd64.pyd,sha256=zCnjUGVoVLyAggTdg-RfG2yclIRNJhV1gI2SWahua6s,17920
scipy/integrate/_test_odeint_banded.cp313-win_amd64.dll.a,sha256=MSQi3QcUsuXthpqc38Hw6wIxudzajfRw8pH6xKew4_U,1688
scipy/integrate/_test_odeint_banded.cp313-win_amd64.pyd,sha256=bdvmS6Z8H3tv2frChD1zjpOrjBfaqiHfitZIezgzE_s,557568
scipy/integrate/_vode.cp313-win_amd64.dll.a,sha256=1R-vff7fCYqwxqNyIbLnV-uwADPYBBE5jxAYxc61LtM,1520
scipy/integrate/_vode.cp313-win_amd64.pyd,sha256=SHWLjbSvV6XShDWJOxjsm6sGUoSpboav6bDir-Wrh0Y,617472
scipy/integrate/dop.py,sha256=XFCkBLyUhdNiEb31DGYdiiwYvmkS7FOwUC4HS2RA_g0,437
scipy/integrate/lsoda.py,sha256=KtndEiRbVPejH0aNBaj3mSHWayXP-wqXSoMZJRPWhAg,451
scipy/integrate/odepack.py,sha256=TxsXidD25atNMK5kKXD-FyNCLXnzrwCdVB1hP4aLSQ8,562
scipy/integrate/quadpack.py,sha256=0DHbM39zByxQkBR11xCokuwuJU41YT9bI74hS1pIrXI,627
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_cubature.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-313.pyc,,
scipy/integrate/tests/__pycache__/test_tanhsinh.cpython-313.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=iXulnWVSpTvpWx1cV03kF2IIzIfSx6K7OHm0iGe4aAI,6549
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=efFXjoYAbYTYbfWbc6IPVDGyBKD3btOArRlT6bO1yLQ,9414
scipy/integrate/tests/test_bvp.py,sha256=2Wya2CHkcCvnS3tnzjrqOdTbgc0udhb78IHIkyxP8g4,20937
scipy/integrate/tests/test_cubature.py,sha256=SPNevwBne_ZwA7qyCKn_aHTosG3U-lE8SNLBzL35ihw,38436
scipy/integrate/tests/test_integrate.py,sha256=AU-GzheWyINS5II5bJkl0Ym7TR_w0q5vJQKobiUm1ds,25451
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=gxhN-laKMIGuszYdOPBYYgBIhOpykikHsTTHdBw27Sw,28746
scipy/integrate/tests/test_quadrature.py,sha256=2ZDUx23rqZbzuTM3oMYz5PC0oQ1O4fUwUKjUP8joDrg,28840
scipy/integrate/tests/test_tanhsinh.py,sha256=pj7-5rIUDv32UsXTRY8pw_XResPmy1I8IfrialEUwGc,46055
scipy/integrate/vode.py,sha256=Gkx0nK3_UbOznSvdghjCtUQx4qpSaZzB6rgltKjP6UU,439
scipy/interpolate/__init__.py,sha256=W4fEojv1wWc9b5ceDvJVxGik4ghweN34KL110pIqx-s,4301
scipy/interpolate/__pycache__/__init__.cpython-313.pyc,,
scipy/interpolate/__pycache__/_bary_rational.cpython-313.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-313.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-313.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-313.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-313.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-313.pyc,,
scipy/interpolate/__pycache__/_fitpack_repro.cpython-313.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-313.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-313.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-313.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-313.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-313.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-313.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-313.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-313.pyc,,
scipy/interpolate/__pycache__/dfitpack.cpython-313.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-313.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-313.pyc,,
scipy/interpolate/__pycache__/interpnd.cpython-313.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-313.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-313.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-313.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-313.pyc,,
scipy/interpolate/_bary_rational.py,sha256=ABxHr48z2ihgkdp7BAaWZCEqEGu-hX5aOUl6YfGjS7M,28687
scipy/interpolate/_bsplines.py,sha256=RmkH_NORmFC2QxSzlqfv18tWLNFuFT-CBg0p41fKbAU,87244
scipy/interpolate/_cubic.py,sha256=kqryR14s86AdcreQ6veT_ZqSxmyFMCkNxMbRJt-j85U,39368
scipy/interpolate/_dfitpack.cp313-win_amd64.dll.a,sha256=loY4uakoD8Hbx9wsAZympbOlkjpJa6iA1WWskLVuigE,1568
scipy/interpolate/_dfitpack.cp313-win_amd64.pyd,sha256=A-kjCSX0qFMfl2n_BU-yhMDOs97SF3b_1CX0mywbX4E,675328
scipy/interpolate/_dierckx.cp313-win_amd64.dll.a,sha256=KWtQx6R5nKIzXHXy1tPimZce7wo9Jj_M9VLfWDac5ow,1560
scipy/interpolate/_dierckx.cp313-win_amd64.pyd,sha256=f4CiorlgCdMLSG8HJ7FPrHXYOiOqicpCNPqV13-GceA,985088
scipy/interpolate/_fitpack.cp313-win_amd64.dll.a,sha256=XqvnBy-9jVuwEXbAXDGUCWkEAHy1Ucna2Wzfa3Eh6L4,1560
scipy/interpolate/_fitpack.cp313-win_amd64.pyd,sha256=kXczRHA9wc2EnY9goZ-XkHzg9u_lHrlPe0MnYeKFnzI,420864
scipy/interpolate/_fitpack2.py,sha256=DOvqpfv_599KPGdE0RuPBvvMyeKJK6Ww3mZhvyix3mY,92318
scipy/interpolate/_fitpack_impl.py,sha256=O7v-m0OJ3H5z7hDGwgmWkJCnzIZ4cKLVv7ip2MWTpB8,29321
scipy/interpolate/_fitpack_py.py,sha256=Wf3EYgE1kZLvCe_svAK9UdGkdsLgr34oYJU2vz_t3aw,33055
scipy/interpolate/_fitpack_repro.py,sha256=l34VMCagHrAmEwcX5ErT-PJTrZoO_eQfiDXfLzyH89U,37977
scipy/interpolate/_interpnd.cp313-win_amd64.dll.a,sha256=MRTPf-qukoF1QVZ7vCjAGVnGWwUnNnkfhtmbF67iT3Y,1568
scipy/interpolate/_interpnd.cp313-win_amd64.pyd,sha256=pYWcZquFxzrJnjfHE8-VEQIQKLOUmQ-GUxVyYFxHdkE,277504
scipy/interpolate/_interpolate.py,sha256=oW6xRSwSDsyDjuqJYF_zIeO3j9U7Otvwhsf4rBwcArQ,82705
scipy/interpolate/_ndbspline.py,sha256=OfKV9fbXk_IuWGl83Az_ay1hIejzOYoF0BsEjLDIlhY,15012
scipy/interpolate/_ndgriddata.py,sha256=UGmOD8kf7ev1fn8ZaOvFbK_y3P5a1ZvFr-LqpfBHKaU,12397
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=IUzUGRK1lp26lsL5uoHPHDWwquI4-jDIBdXt4LsUjgI,39982
scipy/interpolate/_ppoly.cp313-win_amd64.dll.a,sha256=rlrkQ7v6x-1DgMwZtEhofZZkxwlh3JDfY-AXg46nk5I,1532
scipy/interpolate/_ppoly.cp313-win_amd64.pyd,sha256=jiryE-JBetceH_BgsFLsT8cVuHMbQctkQzcNpX3WEhw,304640
scipy/interpolate/_rbf.py,sha256=BYZxUnhL79yMy_bp8w7wbwxFAe-rDpFjcmyvy_4SUVw,11971
scipy/interpolate/_rbfinterp.py,sha256=RPWkJO9DA5rC2no9jdCY_nTWr3BVYiisnPhO5oMQrHA,20272
scipy/interpolate/_rbfinterp_pythran.cp313-win_amd64.dll.a,sha256=vqEhQg30nITQT0JVUbsTFGCTHosiQqU9Ne7yGjwrA4E,1676
scipy/interpolate/_rbfinterp_pythran.cp313-win_amd64.pyd,sha256=-YBZsjTS2X3pDirgxMWfYW5JAUhbSxkxSKhB4tsjKTQ,1165312
scipy/interpolate/_rgi.py,sha256=Z_eW_bzIf4JqEfEoI9CeTPCzGjlfLpR7xZ77kv6uLdg,31538
scipy/interpolate/_rgi_cython.cp313-win_amd64.dll.a,sha256=2wb8OawzJiqeTJo6SkKErtl24k4rmAuSwkPC57zlEHE,1592
scipy/interpolate/_rgi_cython.cp313-win_amd64.pyd,sha256=CLYJUa4W3CSGRtc6tvOQROkHnk2yRix6k1Wseiq6vkM,150528
scipy/interpolate/dfitpack.py,sha256=qeY-z2we03fjo6vgWogVzTE71xYGS2PM9gQ_yXnOemg,618
scipy/interpolate/fitpack.py,sha256=xFDtIx-OMu6QIsF2S3x4nEF7ZzT2c35xGeQBWDJb-Hg,733
scipy/interpolate/fitpack2.py,sha256=52AzGRbO-L23DStEBL_litxtMNILQMWLPx9fmQtyTXo,846
scipy/interpolate/interpnd.py,sha256=5KNDB_3J1yiPakIueVWF031ztzLPCGRTYsIrPNoyCe8,728
scipy/interpolate/interpolate.py,sha256=0MvjiPJfzyQO9_FExxZnTEq-ewR6LjiEtFF6M5RCb_U,784
scipy/interpolate/ndgriddata.py,sha256=QNqLbK1fng-TjM82puP068Stzq-TmFOSkb4mXopUJXg,659
scipy/interpolate/polyint.py,sha256=zfIlcTch3ZLhbT34EqhsfsPOi8h2X8lbnIn6u4EMgQI,696
scipy/interpolate/rbf.py,sha256=Y7ARd1hhej9QRSWR6DuRw4o_s0Tavm5DXYIU5DOdpRM,537
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_bary_rational.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-313.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-313.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bary_rational.py,sha256=9czKeMogY8ILqoe9FS2sOOaOpueoQX2LRSG7KrhrJwA,15816
scipy/interpolate/tests/test_bsplines.py,sha256=Jj0HoBVVGMyJWNm55-NLli6TQv_1hIz2tlwOfvypkv4,135633
scipy/interpolate/tests/test_fitpack.py,sha256=veYpMQsjwJTIQJUnBtgvQurX1yngdoaQgzPf9v8pvl0,17108
scipy/interpolate/tests/test_fitpack2.py,sha256=TKKaRG3v6HdxLCR17Aedd8X8OrBsQdZBrSYpmYrmLtg,62720
scipy/interpolate/tests/test_gil.py,sha256=yUdV9r0g-Af6jbmHtrgtYp3eVaE970RjL1MDqCPoID0,1895
scipy/interpolate/tests/test_interpnd.py,sha256=YyFWFKTguHhqIgVA9HXtaP6CWXk4rT3ddphayHPlovM,15977
scipy/interpolate/tests/test_interpolate.py,sha256=f34MHl3Mmg_CogklvjwKNr0VYjymXbgHoEblQiiYiRw,101833
scipy/interpolate/tests/test_ndgriddata.py,sha256=HFR7NlAVXd-aK0Oc5uOryyCCQVpSVknYoyqha527zTI,11333
scipy/interpolate/tests/test_pade.py,sha256=Ufu3_W5AQc749WLTT7ogiW_Vi0mYfz5IAQYV7Kjb6bE,3975
scipy/interpolate/tests/test_polyint.py,sha256=zuIU5G4V18snGsQCMNIKosZsphhKnt0fUI846NMtYEg,38268
scipy/interpolate/tests/test_rbf.py,sha256=IyX3UZCMF3f0yxCDp9XZJW1ntOb17sNOAKIk-fZpilU,7267
scipy/interpolate/tests/test_rbfinterp.py,sha256=S_IdiAWjWt-wTYHYspwyHncGYWZye-td7oxuzO2vbM0,19628
scipy/interpolate/tests/test_rgi.py,sha256=8c0a_gyH5nf9yyGLveNLzEm9bHkjGe2ZnaSAI4t7sMo,47429
scipy/io/__init__.py,sha256=3ETOeDKr2QQL-Ty2qrrzxAzqX09sfJzsKaPm5r8tZyA,2851
scipy/io/__pycache__/__init__.cpython-313.pyc,,
scipy/io/__pycache__/_fortran.cpython-313.pyc,,
scipy/io/__pycache__/_idl.cpython-313.pyc,,
scipy/io/__pycache__/_mmio.cpython-313.pyc,,
scipy/io/__pycache__/_netcdf.cpython-313.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-313.pyc,,
scipy/io/__pycache__/idl.cpython-313.pyc,,
scipy/io/__pycache__/mmio.cpython-313.pyc,,
scipy/io/__pycache__/netcdf.cpython-313.pyc,,
scipy/io/__pycache__/wavfile.cpython-313.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=fJDOzF4m-Zne-Rz9gl7AGdnZNhyN7zrJ3weY5xs-Jl8,17847
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-313.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cp313-win_amd64.dll.a,sha256=MR47QSNofygX7fgccyAKiGRHE9m8zGrCRx3hFSMXfTY,1568
scipy/io/_fast_matrix_market/_fmm_core.cp313-win_amd64.pyd,sha256=vY6NhTY4CZIRicaW430ip6tiojrs2gEx7NOp5_Snsm8,2761216
scipy/io/_fortran.py,sha256=vx4KXq-MZsSRFnw0QMcxqKmEClqKkSfOY_nWQ4bnOHI,11247
scipy/io/_harwell_boeing/__init__.py,sha256=bPKU-59CohUohubhJBpDFqtFbCo3v3Ptl9hb-FHaFi8,171
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-313.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-313.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-313.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=6jZKE4oYxLtEZHlKonBuD2HKb3pgnd-JU67fcFtW5U4,9319
scipy/io/_harwell_boeing/hb.py,sha256=7aH793tcXwtp92AqL_am3PWHuaGNRYRZnpQDNGXvKZ8,19975
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-313.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-313.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=E3uHrWQdUXjdjbNpkYUZ516Ug4nPjWucK8fJ9ZBt77I,2457
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=NrTo3Zbbk8-VwffX3dINDs4MenXNrKRJvitwLdZtXyY,2586
scipy/io/_idl.py,sha256=8c-3R0bg_1IPIOVnj7OkO9l0xQcdMf0E-dKf7PmOwP0,27917
scipy/io/_mmio.py,sha256=4OndoiMFXb3R1TTeyCbfUvwK12Pmm4qG3hGxEFXx4-c,33062
scipy/io/_netcdf.py,sha256=sb8GdupJ_ZpFmKQN2OLqYGtM6q4EvylxwwBL6buN0v0,40738
scipy/io/_test_fortran.cp313-win_amd64.dll.a,sha256=pQjg15uGw85U7DA6jTftRev5lr4KVdch52cngjXC-dg,1616
scipy/io/_test_fortran.cp313-win_amd64.pyd,sha256=8GRgrxQHH7KybYD7wSpSFUTj1a1M0dV1SgTUmOuajaI,382464
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-313.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-313.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-313.pyc,,
scipy/io/arff/_arffread.py,sha256=9Tx1iwYQH1lT4H-EiiBVEZeabUfSrAwY0WNEXDFLdcI,26625
scipy/io/arff/arffread.py,sha256=RMsdm5rayUdCfdr9psq85Bb15NxLk0z8A6nBeaCopNw,594
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-313.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=x5OIgS0kW-PtBlMud4AuTrvPYDZ4aqmPzCn55JAEWlM,13515
scipy/io/harwell_boeing.py,sha256=uZ7NZBLRwQ39GlELGYby_JsIHjMDp1NGbc7cjWq6OFs,555
scipy/io/idl.py,sha256=FgICucFl-tR94QRdtx099cFD8YEbGJ_fH9A1cT5qRGg,521
scipy/io/matlab/__init__.py,sha256=KrKS46UoddlqN2MnkjehxcI4lcwEBh9bLGnxZro0wZo,2313
scipy/io/matlab/__pycache__/__init__.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-313.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-313.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-313.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-313.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-313.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-313.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=5QL5iQVv3Uj_dki1NOs1m5f1yiu2VNF83kXNNYOE_58,2058
scipy/io/matlab/_mio.py,sha256=l_k1Ht5iE2jYEkYs0JU1U7t3YiBxlcQzbLo226fIoTg,14185
scipy/io/matlab/_mio4.py,sha256=28UPTFY51MGcAh0c6-bjLKAFScgmqYOMiNh4Jw2298w,21625
scipy/io/matlab/_mio5.py,sha256=ntyT0vNvgaLPUenhwpTG3zVZXtgOHUEuo-wHa1YZqZU,34890
scipy/io/matlab/_mio5_params.py,sha256=f5HyjzbAwkN4zNRwn0N_n01LadTbsmUFGWjflumbxeU,8482
scipy/io/matlab/_mio5_utils.cp313-win_amd64.dll.a,sha256=hRE__7dZM0U_iiBz0w7XAVweZvV4lgvUP2VNngktL8M,1592
scipy/io/matlab/_mio5_utils.cp313-win_amd64.pyd,sha256=Kr2UR0lJl0NSYJ0ZFdV5svaPHXGRsHNwIu-z0aW_rlg,191488
scipy/io/matlab/_mio_utils.cp313-win_amd64.dll.a,sha256=SkR7wL_Ovj5KrUf72tdw46nxs9iLc5guHQfU9raNKoI,1580
scipy/io/matlab/_mio_utils.cp313-win_amd64.pyd,sha256=30GkzED5VqahDde8QDhr6VoO-lmD79AELX1tDxc6UC0,53248
scipy/io/matlab/_miobase.py,sha256=ShBq7n5b5gQVDAsKOa0fyUcDRB2kB0IlFPgI5jjWIsA,13537
scipy/io/matlab/_streams.cp313-win_amd64.dll.a,sha256=AmWV10jAUHEfYR3Exrzsd0eJv0zKr7a8VdcZBHuE1j4,1560
scipy/io/matlab/_streams.cp313-win_amd64.pyd,sha256=3QgPVmhytnddrA6EW9DAxWlBdjes9gM4augc0vfpaSw,111104
scipy/io/matlab/byteordercodes.py,sha256=iyqwM-3YIUCH9LJdOEbvMJ7n2gu6nxKdyb0zspXeyA4,545
scipy/io/matlab/mio.py,sha256=Rd-5E8FJ__4ylQfqBLsiZXuHdOs_kpqW1gu6U8uuEjA,555
scipy/io/matlab/mio4.py,sha256=-r3TPyoOQL2JFpfg_Tkkq1TLGVwU5puFeSfpDfP2_qc,525
scipy/io/matlab/mio5.py,sha256=LDYpi8yS5Sh_1n-U665ILxjhv2k4aEdepb4jv6pdIUc,657
scipy/io/matlab/mio5_params.py,sha256=k_DuRuxmwMBXDdKrrpa2gDmU0ZLudzxyKJhmiDZ32fg,611
scipy/io/matlab/mio5_utils.py,sha256=zAGGUPRweBSnK2VclfLePonc3N63WwH7q4rta4VFBzM,537
scipy/io/matlab/mio_utils.py,sha256=3YjORgt2MmmZvxXLnk680YX-3cs7YVvKxRyMqUfHo4M,535
scipy/io/matlab/miobase.py,sha256=ucyFrqpuWm2GNepC3PB9Zg0xs2bGVqvYPscphlxVHbQ,581
scipy/io/matlab/streams.py,sha256=QjbFVhqfk3IR1ow5SPpE37Be47jJr5169sNTC1hhpmY,529
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-313.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-313.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/debigged_m4.mat,sha256=8QbD-LzoYbKSfOYPRRw-oelDJscwufYp5cqLfZ1hB0c,1024
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=qnMlEE_eFjdQhAfOBWuRv7Q1dE2ub9T8BONfIXTfoCI,48567
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=1cyNCs5yDs5qoZkgMMM38w4xnE17d_9BuI2_S3hmq_M,1441
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=Ju2SKwhieuAs2AZnt8NhEw1DVc_BpLts4BvYn2SaaOE,1492
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=3T0aC_eSvutWlfNsKOL4JCUUqg9jYEhGbPgJ4ZdynpM,7638
scipy/io/mmio.py,sha256=qDHalmZoj_VZLTcaO65zAXs-1ZF69aeKoF9p1R-Mh1w,543
scipy/io/netcdf.py,sha256=YaRtHSM0jdaFB5W-Og7vU6CsgaVhEOaogTwObhuHTOA,550
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-313.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-313.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-1234Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav,sha256=GSJpCuezlvHbhP3Cr4jNWmz4zG46XZ6jci2fWtiMN0k,17756
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav,sha256=iSGyqouX53NaEB33tzKXa11NRIY97GG40_pqWF_k5LQ,126
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=GcN2kEe42JNuTZEdXDGHv8gxgKnSb2z9Ny2Bj4MVcho,8873
scipy/io/tests/test_idl.py,sha256=e-MW2PmUwlp3JWkyA1hvpgNZfKg1Ag2rLrvAtaQ8d9M,21052
scipy/io/tests/test_mmio.py,sha256=OyV1tVUwzL4bkYWbkxIlXLSuO1yhENxGnKYbNbaTyo4,30081
scipy/io/tests/test_netcdf.py,sha256=wYYxRab-AMdarFYb84TIlhhoU9n0xwtIlfeWdnvfmrY,20009
scipy/io/tests/test_paths.py,sha256=aLSuVIBgbDHpgeuqvT_cAqAPQVb266T0oJHNLhvgJmk,3283
scipy/io/tests/test_wavfile.py,sha256=WhwJnJv9KAC4mzkjHAKiw6JYAlordHkX8kbOu5DNOfA,18913
scipy/io/wavfile.py,sha256=bkR98CzY97EjeMOezpKdBiEEBvzcev-5Xh4NRjq0pIo,31403
scipy/linalg/__init__.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=byYkkwVKNJuoJzNiksMlhQvd3RxaZU35EnJImC3bhlw,7753
scipy/linalg/__pycache__/__init__.cpython-313.pyc,,
scipy/linalg/__pycache__/_basic.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-313.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-313.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-313.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-313.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-313.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-313.pyc,,
scipy/linalg/__pycache__/_misc.cpython-313.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-313.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-313.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-313.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-313.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-313.pyc,,
scipy/linalg/__pycache__/basic.cpython-313.pyc,,
scipy/linalg/__pycache__/blas.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-313.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-313.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-313.pyc,,
scipy/linalg/__pycache__/lapack.cpython-313.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-313.pyc,,
scipy/linalg/__pycache__/misc.cpython-313.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-313.pyc,,
scipy/linalg/_basic.py,sha256=C8nbFkfYsVBKjId-iG2yd7iR_Wm8hEeQTirDhGnKN_M,79783
scipy/linalg/_blas_subroutines.h,sha256=YvD5DtaiV0EWMiIiSk6Qq8y1wPYe-F_az_AC7HdgOZc,18354
scipy/linalg/_cythonized_array_utils.cp313-win_amd64.dll.a,sha256=Oy8cD8qyLKe79P-Q1S3VhzvfqjOvNsWBmh3pKA2tk0g,1736
scipy/linalg/_cythonized_array_utils.cp313-win_amd64.pyd,sha256=islQ2guMxl9lSE4yQUn0VpBT84DuWFSbpl_pLfG8qic,419328
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=8LZNxiHfbIUhWcFrovu3HHvP3DryuNSPyeD1BlZDom0,64077
scipy/linalg/_decomp_cholesky.py,sha256=1y0ZQ0DmGz8RfaKQJKhgqZqQOMsXjxr4-gTWSdwhkMo,14605
scipy/linalg/_decomp_cossin.py,sha256=88u9OEvLnwn73O3_3dWOM9abwXbwiQssH1BYu5UXbuQ,9861
scipy/linalg/_decomp_interpolative.cp313-win_amd64.dll.a,sha256=MkC5_yVTOuy4Bmpw3DKQHxB5B-n1mW--_3YbzXN6IN8,1712
scipy/linalg/_decomp_interpolative.cp313-win_amd64.pyd,sha256=jb_4gNzl2-fxe00zlmRoymy347KZjNEzIAH_e8ryAPo,777216
scipy/linalg/_decomp_ldl.py,sha256=3orGtqQib6OcY-RZGTmaEKg2jue2QFHERGfccVg-h4c,12968
scipy/linalg/_decomp_lu.py,sha256=_nEol4QE2DA_5NI1IcAGP2AiClkU8nI_-4U63cCezag,13657
scipy/linalg/_decomp_lu_cython.cp313-win_amd64.dll.a,sha256=EOy1dntc_7dbzrvfLClnyLDPY4SDYPdhfnGwz3sA2mo,1664
scipy/linalg/_decomp_lu_cython.cp313-win_amd64.pyd,sha256=VCk5dS0riQZ0SuU260Ar7SSJ8wJwBswWVJLB__R4Eh0,126976
scipy/linalg/_decomp_lu_cython.pyi,sha256=bwyBF7nl-jGGrmj_D16QSXx7MBXAVtU3k4eswqKohCA,297
scipy/linalg/_decomp_polar.py,sha256=Zkr-IvnCjontMTo3ZM2WzkZCdAcojozUUMzGYT5BmRs,3767
scipy/linalg/_decomp_qr.py,sha256=blVNjtFGpL74wzHDklLD38Y2_8Bh8e8bhxm0XQA3iBw,16000
scipy/linalg/_decomp_qz.py,sha256=SGKJ8UdoD-ftC5LyHRrjEyi1tHXxUAxkxJMex-k-v7Q,16907
scipy/linalg/_decomp_schur.py,sha256=V_0VT6GrGLKAKAvPxmutUR4vWQOIuK0k_CCOer3Uv_s,12509
scipy/linalg/_decomp_svd.py,sha256=DIMWsf47m0-g1hROgseASGTCL7yRtRh7PWLjkUhtgj8,17684
scipy/linalg/_decomp_update.cp313-win_amd64.dll.a,sha256=oard5nxr57l6Ym0Mrn7FErxsEl825_8WByy_aG_wjSI,1628
scipy/linalg/_decomp_update.cp313-win_amd64.pyd,sha256=VNZy7rPn2MsrVibqEtVX0nYNTacHMTaJb90dNql4zBI,316928
scipy/linalg/_expm_frechet.py,sha256=JDQ7vZDe3_5CGuDPUNEs49BXNtUb1Etqg4f-1IZm6gg,12859
scipy/linalg/_fblas.cp313-win_amd64.dll.a,sha256=Io172qbzRB519FP4sG1gxIb6WUm2f6nG0EZeMcvk-WQ,1532
scipy/linalg/_fblas.cp313-win_amd64.pyd,sha256=eqquNlTF4Ean2KQCGRO3YaUyM4cy3ltevwU-ML-BMzQ,737792
scipy/linalg/_flapack.cp313-win_amd64.dll.a,sha256=k8JBzQy1MpztNXiOVuee7uuKETzEMFaul18qz3nKUOw,1560
scipy/linalg/_flapack.cp313-win_amd64.pyd,sha256=KgKfHAhzccA5L1FZ67ht_G_sm6G-UR48HcitBFu8UMg,2273280
scipy/linalg/_lapack_subroutines.h,sha256=m28-LKqeKyorgxl96RszhKAKBqbDSbvySmNMV_jnjfI,240854
scipy/linalg/_linalg_pythran.cp313-win_amd64.dll.a,sha256=vJ1As-8qcNqzl6nykTL-VY6lcMX4GegVaYpABEgkDXE,1640
scipy/linalg/_linalg_pythran.cp313-win_amd64.pyd,sha256=t9qRc23spAMhN42UnW2DYVuvGSbd6xb65kNLZHDY614,1061888
scipy/linalg/_matfuncs.py,sha256=SGiNjF-ftp8sh0d30W6GH2sC2GgSZqE9S_KhdfcoL5k,32838
scipy/linalg/_matfuncs_expm.cp313-win_amd64.dll.a,sha256=39h6huj8uzME89Wr-6TsWd5XtMXDVhd_agExppSOmTQ,1628
scipy/linalg/_matfuncs_expm.cp313-win_amd64.pyd,sha256=-AK0fcbQRE_ZTBBa2ROJnVU_r1S4EUsEIwLgxP7eGCY,229376
scipy/linalg/_matfuncs_expm.pyi,sha256=oR8iir6U3yDw0TBht7N_hOz2WtJpliJpf-goe8tRRqU,184
scipy/linalg/_matfuncs_inv_ssq.py,sha256=r9jQZ3UTugs8quso43y-elJ3jScg60XZu2oJuJ1CDsk,28981
scipy/linalg/_matfuncs_schur_sqrtm.cp313-win_amd64.dll.a,sha256=CAkKwoWdvJdWrMj5e2ua9sLxk9hDwCs9wVGfD4C0E_M,1712
scipy/linalg/_matfuncs_schur_sqrtm.cp313-win_amd64.pyd,sha256=fCH_c66-knfwXTgPDrtPOXuHou50RYwr4spcRAts69w,210432
scipy/linalg/_matfuncs_sqrtm.py,sha256=2osyeVy9s5E_EK8yQAQoXbUs15hlCiw3zV9ZV46PYDM,3530
scipy/linalg/_matfuncs_sqrtm_triu.cp313-win_amd64.dll.a,sha256=wRS7RviV7IqFdoL0ZVK3kV6u398spIB58RZAWJwmeiM,1704
scipy/linalg/_matfuncs_sqrtm_triu.cp313-win_amd64.pyd,sha256=cXM3gh2NlZzr17HG7zNijzPJ3kMM0hyz2L_2j7Lrx4k,133632
scipy/linalg/_misc.py,sha256=TQ8dy9SwwcUNl39NiworNgvWE6UoqW2mG2sOYW9cefE,6492
scipy/linalg/_procrustes.py,sha256=J7mbfgM7rhfHPEcsP4CXRBi3B725ExvKb2BAluGKYOo,3719
scipy/linalg/_sketches.py,sha256=qO9mG6fXqFWAy_bHAz_mHcjDUtxx26DGIZ31TmDJgz8,6798
scipy/linalg/_solve_toeplitz.cp313-win_amd64.dll.a,sha256=Ah0uxUoOheiyzvbN8GiGuEIxmk4NDv37tInnZeDqUko,1640
scipy/linalg/_solve_toeplitz.cp313-win_amd64.pyd,sha256=r1HjOoJXgBsrkGd90rL9xOJ5DbOzWPqqXvjBpu5wss0,150016
scipy/linalg/_solvers.py,sha256=9WJaMsTLVifxhrG2lZF1hxOc7geOT5JupQEYQLtvsbw,29960
scipy/linalg/_special_matrices.py,sha256=ONKc2C-SXfCzzMjxz4nPDDABErkAGLoU6937eFFKpeU,41712
scipy/linalg/_testutils.py,sha256=s8zF_A9X4ToKUuwd5QUToC1YoQg7QcFr5UgruaCat2U,1872
scipy/linalg/basic.py,sha256=cY1Q7GWjObxvdOHgB8EQWT3TiQbmXzWfyLOAOpvAGN4,776
scipy/linalg/blas.py,sha256=66RDMO7O7Gjb3EWFvrpvmcmC6SOeWgmzKJa208RVLq0,12277
scipy/linalg/cython_blas.cp313-win_amd64.dll.a,sha256=cD0qIjjebWPO8xJS1klaYlHbMIecxqDIxe7nKg0qef0,1592
scipy/linalg/cython_blas.cp313-win_amd64.pyd,sha256=_SLxJbI6JhcCVmxgQFa_KDQ8Af9ytVQ8gW5ZqoA7UC0,155136
scipy/linalg/cython_blas.pxd,sha256=voPCvko12ZS_Bve1IvptSdsq6fu5KXa8ztkLU-zmVF4,15761
scipy/linalg/cython_blas.pyx,sha256=8dESd8wcDFsTuYBTDdOsYKjgPHECuLgeTntQJzvxWXA,66736
scipy/linalg/cython_lapack.cp313-win_amd64.dll.a,sha256=RDCrvpY2AYeoHx2P2aRB8WMyujyIesObu-RGzH9k3Q0,1616
scipy/linalg/cython_lapack.cp313-win_amd64.pyd,sha256=BCJDLlqIQd27pHp8kmIXT9eI205oDJ6zvbFmexneRtc,522752
scipy/linalg/cython_lapack.pxd,sha256=Up9I8aC8Q6olO0WmMfipi7g6micBTiKAztfpZ0zXOeE,206084
scipy/linalg/cython_lapack.pyx,sha256=XfsRB1Aw-8zVeZ_1m20rQNs8msDiHCtki2iurpKoZao,719057
scipy/linalg/decomp.py,sha256=lHjiR7giOLALFVRUnaLgUZtzkJlsOvdbgdxJSezWzJI,731
scipy/linalg/decomp_cholesky.py,sha256=aWQWVpSmXuxOlmibf4weokAbcumzt3B4bWHNcaSeKTU,670
scipy/linalg/decomp_lu.py,sha256=OpAhkh9LuGzeF3xsNrV7LnLNH1r9BKIRjEykPq6J7Mw,614
scipy/linalg/decomp_qr.py,sha256=dj9Ip_NL9_DhbTMvOu3Uf73KFxzAWw8Zk5AkW_x8Ig4,587
scipy/linalg/decomp_schur.py,sha256=BTSI6wpUMZewPGsaY_t_iJVWH00YO1unn3J8M5cEA0E,623
scipy/linalg/decomp_svd.py,sha256=6vTAujJiNDCu1-UTkPcPmWkT9s2BXSi9H9d_o-RZCow,652
scipy/linalg/interpolative.py,sha256=3kYXK81YeUu5HrAiX2i6GyDP--ubc201NMHbOp52fYQ,33746
scipy/linalg/lapack.py,sha256=y8wPRoGu84I4qlGLpKMKrqaj25aGdwh6JME2E8o9MFE,17018
scipy/linalg/matfuncs.py,sha256=vS2L0NIDTE4Q8FSeE3uljLVUqmhsTXY_JUbTLvbYsuA,767
scipy/linalg/misc.py,sha256=SY3ronlv05KREUi0vhxT3ruSPUg6MZmRoZWgXJh-7ZQ,613
scipy/linalg/special_matrices.py,sha256=0gdutxoyqAuYHgKppzgGBgDS2pB1e7w5DwvkO7TDWD4,779
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_batch.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_extending.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-313.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-313.pyc,,
scipy/linalg/tests/_cython_examples/extending.pyx,sha256=TBlMNQgNLtDDyTwvxwLlTrqQ6v6IMkO47MuvZP2_6ao,910
scipy/linalg/tests/_cython_examples/meson.build,sha256=YssVaDcKQJ7dv46wNiS7m2nU1_X5YOmxq0wQZj8RFmo,880
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=3KOVRdAPeIguaNAeRXbbBWDEIiFx4itscm79G-wjEhI,81764
scipy/linalg/tests/test_batch.py,sha256=vfF0Saq3bv1UcNy1gxdDGxhgKv4luefXnyoEfkifatQ,28254
scipy/linalg/tests/test_blas.py,sha256=i4jUu6rC98WOiKXzQtRCUl6-QdcQlFRJvAhnBPbYipU,42856
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=FuwDgBZSL9JtQSLjW5-0Bueom1sBYLd4iCwZT2EGX3k,818
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=bp0XUxaB88qEu4gZHr5w2v-IhsPLDRDtYOeBlyOswq8,4222
scipy/linalg/tests/test_decomp.py,sha256=HMIljd_gAx79Ccx0g4Dr1qaEabUd8sak4iCuhJ8z51o,123369
scipy/linalg/tests/test_decomp_cholesky.py,sha256=mqJiEJSLV72YnoI9Ht0pT6ZtJDgaRMW1VPlcTHUPDt0,10011
scipy/linalg/tests/test_decomp_cossin.py,sha256=MknQ16fJKyFh-oqYmY_Bi9sMxfFRTM_CQXOlGXY26Ps,12874
scipy/linalg/tests/test_decomp_ldl.py,sha256=keR9KU4uM5B1AZPYl9Xy41oHj3e4Krfn40jx4WDpvys,5108
scipy/linalg/tests/test_decomp_lu.py,sha256=knHYT0tn4gcbke6ybHCEFh2qf0DgpyfaSrRS_g_45Uo,12937
scipy/linalg/tests/test_decomp_polar.py,sha256=Qbz419-_RBk609srtgYEMBI5i0XkvM264NeMciMgcQE,3397
scipy/linalg/tests/test_decomp_update.py,sha256=HYWHmA5zXN3kT_ULj9noBvkuyWL4HH1ythYS3aTjC4o,70203
scipy/linalg/tests/test_extending.py,sha256=Kqi89ZDGfyNf7De04K53Bmw2hwgUFJbMH6b1OCmzjis,1797
scipy/linalg/tests/test_fblas.py,sha256=FoEZpLFpdle1scpFXl5CeVTEH31c5GBxxW5FNPx_Usg,19294
scipy/linalg/tests/test_interpolative.py,sha256=ean0z223e7PvHQlRHHrAPDTcHZJ2jmzsso4OBbIosKQ,8809
scipy/linalg/tests/test_lapack.py,sha256=U0bPAQbmN-gjn7Q7t7pbVQE0dDA7VTf_8LcR71RuSEQ,142070
scipy/linalg/tests/test_matfuncs.py,sha256=wuJllVWr4BdOZ-bSDvMV0woh2mGGE4jFVkTG-A2uaeg,44748
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=U5eyz0CgfamqdrZsXCvY_qy736xhbwKgM0Oe3eybkdM,4224
scipy/linalg/tests/test_procrustes.py,sha256=fjbt_cjZ0C-F5tstRT5nTVm72TBf8IBnxgUlhXcNPnI,7672
scipy/linalg/tests/test_sketches.py,sha256=hfaeXh4LMwA_xFfnvXqb4cUONe_z9lDmAVLKZ3FUyuA,4072
scipy/linalg/tests/test_solve_toeplitz.py,sha256=vl8wkvcTcJ2uaPHt_KL97zVpi0iWXBn2XqC5L4bG9Ag,5261
scipy/linalg/tests/test_solvers.py,sha256=mkCUIauCdRjSWyHaO_SN1h5PrdznzOLRcKDXc3mp1RY,34795
scipy/linalg/tests/test_special_matrices.py,sha256=b3IlSlmviGfezxkbcWouKC5wJ9dP6cd60aZjJyU7nHg,25576
scipy/misc/__init__.py,sha256=QxQqnYCRdiWp6_jsoOKHP4ypuHztbh-3z0r6fdxN4gw,141
scipy/misc/__pycache__/__init__.cpython-313.pyc,,
scipy/misc/__pycache__/common.cpython-313.pyc,,
scipy/misc/__pycache__/doccer.cpython-313.pyc,,
scipy/misc/common.py,sha256=3-Cc2N9gE_yUo98aeXqNgE_JBwWLLWORJumYiBfPHuQ,148
scipy/misc/doccer.py,sha256=mfBxIHwOGDIrxRJN3nh2d9bSo3Gud1Wd0yk-t-CQZVo,148
scipy/ndimage/__init__.py,sha256=hdlCbj826nTtkMBQF9ezsSXQkqeELA-6zIlcnTsBEVk,5349
scipy/ndimage/__pycache__/__init__.cpython-313.pyc,,
scipy/ndimage/__pycache__/_delegators.cpython-313.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-313.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-313.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-313.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-313.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-313.pyc,,
scipy/ndimage/__pycache__/_ndimage_api.cpython-313.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-313.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-313.pyc,,
scipy/ndimage/__pycache__/_support_alternative_backends.cpython-313.pyc,,
scipy/ndimage/__pycache__/filters.cpython-313.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-313.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-313.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-313.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-313.pyc,,
scipy/ndimage/_ctest.cp313-win_amd64.dll.a,sha256=7YdC_7WPzzEaDWKPdae_wamLngws3maTl5hzLuiLwrk,1532
scipy/ndimage/_ctest.cp313-win_amd64.pyd,sha256=3GlBIoC00TJtGF1CiNkqOlT7WRxLYhFDHFDrjErpmCk,16896
scipy/ndimage/_cytest.cp313-win_amd64.dll.a,sha256=3StB_BDQ1iQ6xChEthQu4cvackYuN0BPVoNuD8x7U80,1544
scipy/ndimage/_cytest.cp313-win_amd64.pyd,sha256=Ex-I3taCJH8mdMxSyYR6kqFKIEytw6OoDhXYUvZ0MKw,74240
scipy/ndimage/_delegators.py,sha256=SJSqpeVMkXTO85ZaQKn5pUxQM7r9LwYlEQcsagx9ORI,9713
scipy/ndimage/_filters.py,sha256=4jSaNoo8NmZo8MO7K_A8sYuKWThN6jtzWBysvHjmYac,94155
scipy/ndimage/_fourier.py,sha256=PTcoyytgZc_dFBsPuYriCy6oJv5HVz85zdSppt4GogM,11572
scipy/ndimage/_interpolation.py,sha256=XG8W2JI-QfDiyClsuIUFgWour-ryvOgGClQ40uck2Dw,38773
scipy/ndimage/_measurements.py,sha256=6C3yNGFPs4l0W9UfqYflORFfO445STUB3ScTtwCXUnY,57937
scipy/ndimage/_morphology.py,sha256=g6IqtPTGm0zqIuKUe6Nbsdw5Az0je-T5K-wtkzDGjRc,103598
scipy/ndimage/_nd_image.cp313-win_amd64.dll.a,sha256=YNSrNP3v-W-3bMH-2vly_Ecl-ogEItOk2MNr5XfgTww,1568
scipy/ndimage/_nd_image.cp313-win_amd64.pyd,sha256=VPnq1UHfXBk2jOANKTboiBQ9YGbTAQm9vvKwAw7LIxA,177664
scipy/ndimage/_ndimage_api.py,sha256=Gvk57ROOlyakFYlsk5LO13xilG9G0ebLDs5NyXjc7dA,602
scipy/ndimage/_ni_docstrings.py,sha256=Joj2VqE3IVCo8a_g6_HHWV0-PB_oQF8CKeMmLy_Zrco,8941
scipy/ndimage/_ni_label.cp313-win_amd64.dll.a,sha256=WE-ae_FNC74bGIOb1bC7OVYVerm3PBD5rsJSomryFac,1568
scipy/ndimage/_ni_label.cp313-win_amd64.pyd,sha256=_-zVvywAI_KVba75IIaD20PcVNAj8V3yUmJyFSpawjg,275968
scipy/ndimage/_ni_support.py,sha256=k2Yi73t-erep2yAgH5OvzuqBwPgPVRsn_TVYD65Mc3M,5355
scipy/ndimage/_rank_filter_1d.cp313-win_amd64.dll.a,sha256=7AOxtv808jmYsf-5nbhWi4oZGfitOhUXtnf3ilqcdIs,1640
scipy/ndimage/_rank_filter_1d.cp313-win_amd64.pyd,sha256=508fvZlMlFZpjU15zVwb5e9Qw4kiQ1rK-J-oXgpe7Wk,145408
scipy/ndimage/_support_alternative_backends.py,sha256=cmKtlmD-4Wm3GkNMR-IEY50yJ3U0wND1rq9GWFgb_xs,3061
scipy/ndimage/filters.py,sha256=pBSTBZeUY3XAJCIFdL1UZmB3OM6KJld2jhzPVWHpOYs,1003
scipy/ndimage/fourier.py,sha256=Mg8ym6fd2BXBFSrUiwrW3GUoeDTYXbdOqQe75EJiKYw,620
scipy/ndimage/interpolation.py,sha256=t5_hbCDE3G06Fd-A7wozt_Tndjlbj3z6jRQPtjiBReo,686
scipy/ndimage/measurements.py,sha256=NNTrZLZSbU6hO42Tj49me-S1A-V-pnODbpxkm4ehLOI,812
scipy/ndimage/morphology.py,sha256=6axnW-p_Fp3Bm-21zYoaaz-1SdNxEPYCl8SoeRXwDQQ,992
scipy/ndimage/tests/__init__.py,sha256=nn22sAz766C8FTykt-e2NdCZ6gZ0417aBraFuJCW81M,326
scipy/ndimage/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_ni_support.cpython-313.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-313.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=h5Ah-K1we_d3fyQyS7KFMy6Orsr56BxPnSx5qEBYGaQ,3840
scipy/ndimage/tests/test_datatypes.py,sha256=rvm8plHWjihVqxU1q1xG43szbC4ck3dq_VizBMCblG0,2886
scipy/ndimage/tests/test_filters.py,sha256=9v3xrB_fWK-9JergNV7cKbCxX5IRhE5ty4RNzzkjZ7s,134218
scipy/ndimage/tests/test_fourier.py,sha256=u6D98_xuemfT3QxgD94MFolDLQQrHH4n5b1xpCacfD8,7805
scipy/ndimage/tests/test_interpolation.py,sha256=PZQXGRh_4M3p9EYuP3vV1gT_bljgp5s9W6LhaGk_RIA,62607
scipy/ndimage/tests/test_measurements.py,sha256=9M1NA-004thv5x6IJSb4AWLOGydJr172k8Hm7RUsgUw,60010
scipy/ndimage/tests/test_morphology.py,sha256=z9tC0FVHzH3Vp3NZpXjKr_OdQ5p2hNIMbopj8r8GU4M,134230
scipy/ndimage/tests/test_ni_support.py,sha256=CFzk3WZinGB1dFpamDlzVkrJyBR7d7B3UUyZrhYyGI0,2589
scipy/ndimage/tests/test_splines.py,sha256=Vp-Eh-LfzH8SXc3s2ZRiK3WWULWFktqlLV3yNgvm6l8,2497
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp313-win_amd64.dll.a,sha256=hwiJbQiaXxnVL6KMyxOoIEpO3rHeGW2Belq4Z5wjBbQ,1568
scipy/odr/__odrpack.cp313-win_amd64.pyd,sha256=jtM08YkssbVE3g7mvzvH8Ca2f62Utz9HtxSuLoI7MxA,689152
scipy/odr/__pycache__/__init__.cpython-313.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-313.pyc,,
scipy/odr/__pycache__/_models.cpython-313.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-313.pyc,,
scipy/odr/__pycache__/models.cpython-313.pyc,,
scipy/odr/__pycache__/odrpack.cpython-313.pyc,,
scipy/odr/_add_newdocs.py,sha256=nquKKPO9q-4oOImnO766H3wnLIN8dBZJfqPh8BgKJ_8,1162
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=JS4iJhlalQTyAJ1mfE9KBRBk1q5Wqzhlsu9Wqf8z7t8,43650
scipy/odr/models.py,sha256=_7pQbo0FThkV9yo2NvXC21_SMbNqUiBjeUsxnd9PerM,610
scipy/odr/odrpack.py,sha256=NqRd2QtNcw1-gq4KpkbkddvMF3G78DxKGSFlJ8Day4Q,653
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-313.pyc,,
scipy/odr/tests/test_odr.py,sha256=tG1MmTJGdPr0uANnUUePkHZH8SplKdCGyN5wXQThpPs,22686
scipy/optimize/__init__.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/__init__.py,sha256=OGwMoSAIsdo0TfJihMt4BtW5LxLJi94EPkZLZfqpn2w,13739
scipy/optimize/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-313.pyc,,
scipy/optimize/__pycache__/_bracket.cpython-313.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-313.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_cobyqa_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-313.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-313.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-313.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-313.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-313.pyc,,
scipy/optimize/__pycache__/_elementwise.cpython-313.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-313.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-313.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-313.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-313.pyc,,
scipy/optimize/__pycache__/_milp.cpython-313.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-313.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-313.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-313.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-313.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-313.pyc,,
scipy/optimize/__pycache__/_qap.cpython-313.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-313.pyc,,
scipy/optimize/__pycache__/_root.cpython-313.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-313.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-313.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-313.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-313.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-313.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-313.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-313.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-313.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-313.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-313.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-313.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-313.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-313.pyc,,
scipy/optimize/__pycache__/elementwise.cpython-313.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-313.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-313.pyc,,
scipy/optimize/__pycache__/minpack.cpython-313.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-313.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-313.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-313.pyc,,
scipy/optimize/__pycache__/optimize.cpython-313.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-313.pyc,,
scipy/optimize/__pycache__/tnc.cpython-313.pyc,,
scipy/optimize/__pycache__/zeros.cpython-313.pyc,,
scipy/optimize/_basinhopping.py,sha256=tfXJweDJFG30kx6cmHzmOvsQ9mif7j3L2FKxXH0uk-o,30663
scipy/optimize/_bglu_dense.cp313-win_amd64.dll.a,sha256=fX1yl0h-N8y0ydihQIf9IAX5KRQmA7j5V3IEOJUFHPw,1592
scipy/optimize/_bglu_dense.cp313-win_amd64.pyd,sha256=vClg8ddLdj-cv46YQr5DuLKHfIczs3DHexWr9bEeOY8,197120
scipy/optimize/_bracket.py,sha256=OlEq2bGKl9Mzp0KvSozLMG_a8h8pq8NhfvzXGnGcfuE,31645
scipy/optimize/_chandrupatla.py,sha256=lJkKimI0chk4dhN2j3fujsAsbQQh4GujSfaV9uVA3io,25099
scipy/optimize/_cobyla_py.py,sha256=4T6G5UjLU4gh_OQ9Tch4Vf7SOSF9y4W5XH3T5vAGm9I,11266
scipy/optimize/_cobyqa_py.py,sha256=Ee6UNJ1U68TZODE4Wa2VsT48tQo1IiO_84B2JE5RWbY,3043
scipy/optimize/_constraints.py,sha256=QqF9KPkExHjSUEkaoXeGx0B4EmgezodOK7EhXWzV_Ds,23495
scipy/optimize/_dcsrch.py,sha256=HLVdwRymefDXa8QZQyZjca1y-yUWOfYn0Jh8SDs0RM4,25963
scipy/optimize/_differentiable_functions.py,sha256=dfk2I4dtlBXVgwCep6P-vCXLroAyuevOL2Wcpmt7YA4,30479
scipy/optimize/_differentialevolution.py,sha256=k8wvpNiefo4Mgs210oAKSiFgucRUfM3DqxLBt4q4hSU,88483
scipy/optimize/_direct.cp313-win_amd64.dll.a,sha256=YbJzWC8-JPzWN3Kn55pAR_Pkj9gbHf5LmAZ62-zcNRI,1544
scipy/optimize/_direct.cp313-win_amd64.pyd,sha256=_R_C7pY5SG1N7bW1ZJXGvZn6UjpzVIY99_AO3QDfTZ8,70144
scipy/optimize/_direct_py.py,sha256=ja2N3luv1NofWKa3tRzELii6SJqbBKUXrLMLdwxT5d4,12129
scipy/optimize/_dual_annealing.py,sha256=Lp6kEZgOrNchuu1rOgEPV80uoZtwxtwgC1qb8BuC6ug,31853
scipy/optimize/_elementwise.py,sha256=rX1iBpfnqMwKEHnOgaTj3ejmzFfLbC5khBrHF_BRYrk,33848
scipy/optimize/_group_columns.cp313-win_amd64.dll.a,sha256=Cx462VadV-wiDgCpUxg6NFVhbSn3wcUwOmApsoo_9eM,1628
scipy/optimize/_group_columns.cp313-win_amd64.pyd,sha256=nYPza4UQAs591lmwjiFwPiDM9lPO8MwexIp4PgViCtA,1033216
scipy/optimize/_hessian_update_strategy.py,sha256=iRVRyZDBN5bNJxKAS-upjyqvGnsZCfq_oB-noOs57EE,18902
scipy/optimize/_highspy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highspy/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_highspy/__pycache__/_highs_wrapper.cpython-313.pyc,,
scipy/optimize/_highspy/_core.cp313-win_amd64.dll.a,sha256=lrPAvGlktt_yaDNeNTxUTOofMTepGyC9FwLE4-QL96M,1520
scipy/optimize/_highspy/_core.cp313-win_amd64.pyd,sha256=2oIjfTL5nY_xpeuoEJelOmMbLaQcc0o4HUtAmhfDDGQ,5775360
scipy/optimize/_highspy/_highs_options.cp313-win_amd64.dll.a,sha256=xisUevE3HcIga3slKEP7I2Na_oOe0WWClb8JgBXi6RE,1628
scipy/optimize/_highspy/_highs_options.cp313-win_amd64.pyd,sha256=RaFSaYesoSprc63XYyulz-j-WkIukj0WAEvdECYztJ8,1220096
scipy/optimize/_highspy/_highs_wrapper.py,sha256=HypwHCVyXceLU_AYWsgC50901IRpXGwhJ9UdkHMCmU0,11632
scipy/optimize/_isotonic.py,sha256=4bN5DvfXn-ITUA8W0xDl4xoHSnNjiyAHX-J5uMrVvlY,6234
scipy/optimize/_lbfgsb.cp313-win_amd64.dll.a,sha256=McBoV4qkDqkXaptbDHBXsZc-oj5sxhX-ndQsA-nH4jg,1544
scipy/optimize/_lbfgsb.cp313-win_amd64.pyd,sha256=gSZSWW4SnWAR2BuNk2dBrvqYp2OKto2Ryu3_iz_O_Jg,171520
scipy/optimize/_lbfgsb_py.py,sha256=ojmpWYMC0CcM-otjP6yZ1lxwH6X9tqP977HVFnBhlew,23723
scipy/optimize/_linesearch.py,sha256=0Tou3QXyomA-ufSauAw7fkA-sL-F3bYlaDScJPMor-Y,28111
scipy/optimize/_linprog.py,sha256=7MoJum3RCa_1_6Ur60Qz3WPf4bfeoEfl--Or7nKK6Fs,30995
scipy/optimize/_linprog_doc.py,sha256=gcBNcYe1LEhsRKlfl_UxdAvQwwREItJ0QyKGM3C6xHk,63365
scipy/optimize/_linprog_highs.py,sha256=x_ce4sA9_Do0KDSDcI16lA-POGW6Ck0ThLHBdI8wUKk,17564
scipy/optimize/_linprog_ip.py,sha256=eMEaxXiiAYsLT5HMHeFDMrveLG8Sm9b4PMVqY5EAO9o,47792
scipy/optimize/_linprog_rs.py,sha256=BHV1EnWBqNUMB2fsT6s6A5dke4w2GauDuBovFRvrB_c,23718
scipy/optimize/_linprog_simplex.py,sha256=8oWXIAYtNU0etv8PEJd9EjVwA4eHrCqyyMA0IL5TLV4,25411
scipy/optimize/_linprog_util.py,sha256=NctuyZ9keR9vR8FjQpQJvGWpOGew_xUQCC2ljgxUN6s,64283
scipy/optimize/_lsap.cp313-win_amd64.dll.a,sha256=AIvSt9wHwouMiHEOTQEFGobW8UAxuXSiJOvfxgA82FA,1520
scipy/optimize/_lsap.cp313-win_amd64.pyd,sha256=1E75UYue_ZO32pdGqmDxBOlHpn6diWvyjhfyF-H_gyo,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-313.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-313.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=a97FFNKw6Bcs1DhCEhiuOXfxRK2JhnNGFX6ohUCPSJ4,21211
scipy/optimize/_lsq/dogbox.py,sha256=Bf3PC3WlJQ7tLrvl4R3WE4EaGq4MJi28VgnA98KnNt4,12522
scipy/optimize/_lsq/givens_elimination.cp313-win_amd64.dll.a,sha256=r74zWyxy_cGp2-xtpmQNIDVwk2MNl1mJHUGPSfhbAwM,1676
scipy/optimize/_lsq/givens_elimination.cp313-win_amd64.pyd,sha256=MpyUTb1SfgDNHvEULFAqN-4a1xDJqQ6GywDpiyz-wNY,82944
scipy/optimize/_lsq/least_squares.py,sha256=W2hRdN0i3tZXdBw2qdMnDtUDKLsQN1qU5wXHZew9itg,43860
scipy/optimize/_lsq/lsq_linear.py,sha256=3KmRnle2kTtIuqWA-ZeXS6TI6J-cOTQf7jUbeyf8P7A,15413
scipy/optimize/_lsq/trf.py,sha256=oXSm55tnBngjgEZmyel0E90NQyx6x80UjAye5c-pcys,21103
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=StTbF4wImfidmNKS9AOHGzp3nwryprU3_qP8Bx6k9nM,15623
scipy/optimize/_minimize.py,sha256=Rznp-FrexRwub7qbIN5lQNuR4gGd7UdQwXfbnDkofjw,54266
scipy/optimize/_minpack.cp313-win_amd64.dll.a,sha256=K_Q8d83HnI1tvd35xlQBjjSYx5LPPYafprW7DjOzAf4,1560
scipy/optimize/_minpack.cp313-win_amd64.pyd,sha256=8sVhkwPfOpEDPDZULMreUu1G4oYBWkmKnIA1YNxc5-8,123904
scipy/optimize/_minpack_py.py,sha256=-YAQ049iNdxSn-uxBuelyRBWpCdqDWRQmHzmQbxfELU,46565
scipy/optimize/_moduleTNC.cp313-win_amd64.dll.a,sha256=XVaJaSW-Hugij7VQbzeBc7ZBd5gZ2IzENY1jlNJKrp4,1580
scipy/optimize/_moduleTNC.cp313-win_amd64.pyd,sha256=6w8RWv275tyhiNMoKP_sjrbRCmwYnjGdHOjVnFI5_gA,145920
scipy/optimize/_nnls.py,sha256=bbyVpzYCWWuM5WWik5oYDtyE0YvBh9nobvq5GIGENNc,3009
scipy/optimize/_nonlin.py,sha256=-fdoU1xDzi1PR_WRNcaHtNQlanP-dGebdaU9DL8cMQ8,53332
scipy/optimize/_numdiff.py,sha256=y_SfYDW-C6ybU1v5opigmC4Fma2N6Q-xPhv7KwtCeFY,36788
scipy/optimize/_optimize.py,sha256=LTWMjFCxNOV0HcOYIBl8OpRokoyfcNoEHF1N-eJWqJc,153939
scipy/optimize/_pava_pybind.cp313-win_amd64.dll.a,sha256=FK-JLzxXTUfWskW1uK7uqv1tk0c--DyHizgaBgsfvOY,1608
scipy/optimize/_pava_pybind.cp313-win_amd64.pyd,sha256=-jR1WYLdrYUcZ3j6IwYstvzdpy7tjxj5-JCEB7XA7jA,287744
scipy/optimize/_qap.py,sha256=oRJM7pgx5KDNRuJ-AtBH3DufByhIFhqfs-2Z5icQWIM,30150
scipy/optimize/_remove_redundancy.py,sha256=67SRD11h5B_nz96wna6lLwwicmy-7g3bfMAn-baSTk4,19279
scipy/optimize/_root.py,sha256=fsXLPO77yz_0Vugh5e_SjefZd_xfktN4NMF1yxIMtKA,29446
scipy/optimize/_root_scalar.py,sha256=ojD4Kp67rX0P5PmKVtDmO1jEzP_aGAJEEpBmE7ddgzQ,20929
scipy/optimize/_shgo.py,sha256=Bbu-u0L1InPbmmNrTjszEkI8cgMcdxM2DFamrePs2-Q,64228
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-313.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-313.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=_4GlyHM1qmHfXYlEGuD0-cPrOe_3WNu-e7cagiYlL3w,51448
scipy/optimize/_shgo_lib/_vertex.py,sha256=z9a-UXpMVD8NXa_y1ePp-Va3b7B1tcAQPmRcky1W2IA,14456
scipy/optimize/_slsqp_py.py,sha256=hQGoH6aNMll0IVCRh1SOqxJFIeWvcGJcd_cUL5hFf6U,24470
scipy/optimize/_slsqplib.cp313-win_amd64.dll.a,sha256=HZj7fPlzv6zcYKcMx2KVwmU88Ur-pz14NIOIqVPTC58,1568
scipy/optimize/_slsqplib.cp313-win_amd64.pyd,sha256=5Fr2kpl-mxzYgMI77DmR_-eedhWh91ton6r0i2MOWa0,172032
scipy/optimize/_spectral.py,sha256=U8Rn07c9QolZGpbVjUPnTsKGxmwaDqbAhgy1tu3Tfvc,8388
scipy/optimize/_tnc.py,sha256=fvetbztCdXFrBNwlsG-TLD73XNuT_Idp81RCVzI5ryE,17777
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_trlib/_trlib.cp313-win_amd64.dll.a,sha256=gSQI24CIcngKBwAVvA5HU95hL3X1SRZ12ecRlgTKORU,1532
scipy/optimize/_trlib/_trlib.cp313-win_amd64.pyd,sha256=YR5ZbXlyB7nXqfZV6mStDMzcPowdKZMBa_LrTH0l0Cg,210432
scipy/optimize/_trustregion.py,sha256=tGAUo2TOJO1lknPC6pVubVVKekkLT6Nx37Zunv0XIV0,11776
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=LiYXx-3hfVrah9ll-QzVfd3Na04AOq73erR33ak7IF8,12932
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=5sUcZXLT1KY3Hs_jc-XQrXycFhZgPuMVu1ne6Bfnlns,9391
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=nyMbywb1aRXtc9j9OMrrmtlxTVWHrYlR8WR0i0xWecw,27104
scipy/optimize/_trustregion_constr/projections.py,sha256=XV4paA-ngMTqsUToiwhBxFUs-f7209vspJnl_XdoT2A,13928
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=KRb3uJON_uK-yd3FocvUZLz8F3LZRuhN6k-R1Lp8A8Y,23224
scipy/optimize/_trustregion_constr/report.py,sha256=YoydHVRTxO5p8jZ9JZUiZX4nnLkwo-usIsksFEz0B6Y,1831
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_nested_minimize.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-313.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_nested_minimize.py,sha256=fRerrlL48GskudreW9Yu7YI7PG9_bLuQ5ZT97CBks7o,1255
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=iycC-cUIlHuIyIq3L7illbMJXznC2vSW6rJkLPxBYac,9041
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=qwaABZKotUpJE1xeAZzf5KrdPUupWnaOUIjBSBihEu4,28287
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=6sn33lkn-_hJo5_5i7vGPZtlguF0ikfxAU_0_GLkMNo,1138
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=kIKtt8djwNsnPkdAIviazef3AER1l6EMJb1k727fh-A,14756
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=CtM1N7VUrHZaUBvlhQ7MkYzbpnCfMRZWe0AdB2xbd7k,15994
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=ikvxL4pBR4fy00TDcTdWafpTl8SVcPgdIeIC89LJ2ks,35019
scipy/optimize/_zeros.cp313-win_amd64.dll.a,sha256=qYjqwiKSrwu7D1ac27aSldKcik5XQ5MsbPvEqQTqpzQ,1532
scipy/optimize/_zeros.cp313-win_amd64.pyd,sha256=4ajSlNzj7U02Lo7PKymvgDkflmMvxCqBlj6t1aVMgBw,23040
scipy/optimize/_zeros_py.py,sha256=fICXpJ0k8BxbxCp_TTbUj-MMS__Jau1UZbM44iB2qUY,58134
scipy/optimize/cobyla.py,sha256=CHOea_YiOpCNJ5blRI6YqoYieCohU96MNYV-YiWADGc,576
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=LEyUcZ_tUfRNMYgyFBYUsHfztui15TviJlLCsbZWrDw,5020
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/cython_optimize/_zeros.cp313-win_amd64.dll.a,sha256=nG02Ffz12z619dNiS1d4KM9ak6lZiivG47LV3eHt3n4,1532
scipy/optimize/cython_optimize/_zeros.cp313-win_amd64.pyd,sha256=ZcVuj9in1UqdPPbYTjYaeAniCLLaYtjt_6O10dz2j60,79360
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/elementwise.py,sha256=PQrGJ57URCLe60H3ogS-0aYT18l8Y8j_2BNM_2Qafbk,1228
scipy/optimize/lbfgsb.py,sha256=wRXQnUcc8-ePQOMmqVM1D0h-ghuhD_qyW_d35nr6YeA,624
scipy/optimize/linesearch.py,sha256=KiorxXY20ql-mVkDQUdUppTlg_KBES3pCfJHNL8XZwA,553
scipy/optimize/minpack.py,sha256=uxAnCG7Su9Gxi5u8P-BCbAe-qKwkNKtMvIkRoPdrRRk,691
scipy/optimize/minpack2.py,sha256=NTrxn0tvN_vBTHwPu5mXNV74MKj9MSEVt8xQHzpOuWo,531
scipy/optimize/moduleTNC.py,sha256=0BZtj41NEa1nM2XYN9Df-kmG7W1QJG4PWcY40Zour-s,526
scipy/optimize/nonlin.py,sha256=mtj4mAfZhUjTF_12P3dwy3ZWgyteugyqG8oJBKwkhn0,739
scipy/optimize/optimize.py,sha256=IJdwFy_tYftU9lXIecQaBpWWXNsbeWTYuZJ0P5KkYNI,917
scipy/optimize/slsqp.py,sha256=Re5lDI8vduyuw0x3ytgtDQ5xLPyRKSH4HVkQexbE-YI,591
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_bracket.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_cobyqa.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_extending.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-313.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-313.pyc,,
scipy/optimize/tests/_cython_examples/extending.pyx,sha256=hYoYg2ZMzOBFvTBgsEbe8HEcgmRakP-eCTH5wOdZzE0,1357
scipy/optimize/tests/_cython_examples/meson.build,sha256=IJJmerGghNtTPz5ud2RIKKjNqxLnFlbZxHlOjD2XXAc,735
scipy/optimize/tests/test__basinhopping.py,sha256=T2-1eyKF-NxqQ9sfpiwiSQzz-K_xYjmCyMhVzlC_GS0,19745
scipy/optimize/tests/test__differential_evolution.py,sha256=h-OXQb9GgTLSMEPK23skVfyjkoh8qiR_R-i9arJEh-Q,71221
scipy/optimize/tests/test__dual_annealing.py,sha256=6wYdaJcXGcfegzZHLNw_mjDwzPLIx7NEwj0hAJmnRzE,17056
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=e56MnC7e2X0o20WKuIlDeK2OIkhF2AhTotRP-f1A9QU,11992
scipy/optimize/tests/test__numdiff.py,sha256=Lr_9TEWCtJhjKwoWY06uE2m97a16e2lCWb1LF7FR4LE,35396
scipy/optimize/tests/test__remove_redundancy.py,sha256=U95kmljLfenOn0k2PJ15sn_rRJTmizx0BeINI9SklWI,7025
scipy/optimize/tests/test__root.py,sha256=FGsMKEz4Yju_c3U-3-kzx5P1IyQ8ik3bsH6gSiE9OII,4354
scipy/optimize/tests/test__shgo.py,sha256=gIuH_Tw8LTJNjFKMBliRfHeo-95t0E3NkDeWX3b7h9Q,41305
scipy/optimize/tests/test__spectral.py,sha256=B3d_qUmY55_NiIrAljlKrk4kHDxP8iyb8PVAXFsx_U8,6890
scipy/optimize/tests/test_bracket.py,sha256=vaTJ5Q5DzzNRkKKIhM_T0ZypPZLcc4s8JJyYwxRyhuo,37693
scipy/optimize/tests/test_chandrupatla.py,sha256=TqnMAkQMcfJmF1vWhKbgyywmJ3LQIkL7V3POJJHQUCg,40124
scipy/optimize/tests/test_cobyla.py,sha256=dObo3U0YCjLUE9Y7fUKLNxvRY-YsNJ4_jdeopu-teSM,7017
scipy/optimize/tests/test_cobyqa.py,sha256=B3BkBXExNEEAkIz-ermiq8i_DhIOHRNpoq1V1y7AB9o,8395
scipy/optimize/tests/test_constraint_conversion.py,sha256=ZkppwXo1bLUVhLUUPqsEz6_hvHwXIRHhYvKzc5KCEGY,12849
scipy/optimize/tests/test_constraints.py,sha256=QvTqolZMKCyj1Nsx7eYVjsOZyaPlpAYoIZBZLm5dyrU,9662
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=Da-L9o7BGH8D7_c-Os1aaw5yA5yGaWPWfyCOuPPJNZQ,39524
scipy/optimize/tests/test_direct.py,sha256=W3GWt8VWR-7IP6tFOily5z84z7zZ_fyWYBAdHM3OF-E,13588
scipy/optimize/tests/test_extending.py,sha256=6bUmDqR2TCRMQhYPNcJL4QEkR7JsiJvkYGQazpb6R2A,1132
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=FGxQB-IpuoqUkIa9LeP89aBPV4jiGMqLr8zCw9agNU4,14637
scipy/optimize/tests/test_isotonic_regression.py,sha256=7_lGuoWwYwOdLbUCuezoumQj8haAFMe7RJmK5ztsoOk,7280
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=gEfwgBXOKimbZ1Gyr9_OeNbITB0XoFgf421Ps7Mf7SI,1999
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=ztwwQ_5iPgLWBs02cSR9f4djZDBFZmlML_Ln6RzDhyo,3704
scipy/optimize/tests/test_least_squares.py,sha256=y-9U2umcquiHq9uyWDH_Y4NvejMiHFPf4tN0N-7g9c0,38657
scipy/optimize/tests/test_linear_assignment.py,sha256=keQICz5fM6ZbR8n1qrixfPJ1C9T4rI8cTXosAPLgy8Y,4225
scipy/optimize/tests/test_linesearch.py,sha256=Pt7ElmTI5-Nr5X4kPsBer8sDavUB5-lcW_FYTEx3O_A,11728
scipy/optimize/tests/test_linprog.py,sha256=H6SgPD1Ujala9xngyo49jdSrj8d1EClKgfsqeZy94nQ,105272
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=E9D_U2pp1cKjtmXWnY5VkvCudpHfmgsBlzWjeGTDKTI,11261
scipy/optimize/tests/test_milp.py,sha256=YeH08QZ0OZLU4ZhgJUJM5QBM2ItKnYXl4TohCjz5IKo,18761
scipy/optimize/tests/test_minimize_constrained.py,sha256=6RHyfYs5f8WXuupbyLUsrXedtdh12X1r2b0ND0Pb16w,28785
scipy/optimize/tests/test_minpack.py,sha256=XUC1FFSV7xqdXpLPmPJD2348wNUmdZZbfrxZ4Us6l-M,46039
scipy/optimize/tests/test_nnls.py,sha256=kScqWOjX0g4ifXKwVQ46xPJ8wwYx6N1vfdO5Y_t6ufg,27664
scipy/optimize/tests/test_nonlin.py,sha256=l3NyQiUGLDbHyfhfNIMWYFHayOb4Qh6SjAh2SMI3gJo,20816
scipy/optimize/tests/test_optimize.py,sha256=lNdBktzMPrQygn7kzVGHQpnXkoU1n8rdgh0j5idpYuo,133630
scipy/optimize/tests/test_quadratic_assignment.py,sha256=WHdALCFsgRvZjb0Si9eQ_d3uZMIgbza0djZOrjiRcJI,18053
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=jHT1aUOZ_vVD6KbCZY5cH5c4jOaMZ5bNmdXUuRtYjBg,25261
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=8zX4bUi0eABzmaRzoOazGeH3fZV9FtZxjqxrQR6AdKQ,4779
scipy/optimize/tests/test_trustregion_exact.py,sha256=-2e4G5fjJDK69aLXNpgPw7LEXwpINSW7fZYN26exSSQ,13284
scipy/optimize/tests/test_trustregion_krylov.py,sha256=-zfEH2BIq6H9quqB81wtuPTNmdA9Pnl5Cc2jScMMGik,6786
scipy/optimize/tests/test_zeros.py,sha256=OQzmBXVqr57M4iMx-kegVoxciJKDzIpiAl3TvlDQtds,39074
scipy/optimize/tnc.py,sha256=znhjH4IV17iV6ghdxwE0aj617iodH6OPPL_Afrj88LU,582
scipy/optimize/zeros.py,sha256=yZg_vt7rR_8z5N2i-Oam3rbMiPdas8l9xgib_sAAThk,646
scipy/signal/__init__.py,sha256=mvZkLkOTXQoWo4cIbA_IQ9atzlIjC4c2hW_c8FxtNkU,13816
scipy/signal/__pycache__/__init__.cpython-313.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-313.pyc,,
scipy/signal/__pycache__/_czt.cpython-313.pyc,,
scipy/signal/__pycache__/_delegators.cpython-313.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-313.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-313.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-313.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-313.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-313.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-313.pyc,,
scipy/signal/__pycache__/_polyutils.cpython-313.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-313.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-313.pyc,,
scipy/signal/__pycache__/_signal_api.cpython-313.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-313.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-313.pyc,,
scipy/signal/__pycache__/_spline_filters.cpython-313.pyc,,
scipy/signal/__pycache__/_support_alternative_backends.cpython-313.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-313.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-313.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-313.pyc,,
scipy/signal/__pycache__/bsplines.cpython-313.pyc,,
scipy/signal/__pycache__/filter_design.cpython-313.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-313.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-313.pyc,,
scipy/signal/__pycache__/ltisys.cpython-313.pyc,,
scipy/signal/__pycache__/signaltools.cpython-313.pyc,,
scipy/signal/__pycache__/spectral.cpython-313.pyc,,
scipy/signal/__pycache__/spline.cpython-313.pyc,,
scipy/signal/__pycache__/waveforms.cpython-313.pyc,,
scipy/signal/__pycache__/wavelets.cpython-313.pyc,,
scipy/signal/_arraytools.py,sha256=W7k_0e3AW8Lz3RLPAmi4SXnJMge9N3y1-VZcvetm2jM,8558
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_delegators.py,sha256=P2_I6znObz16tUOs6ovYIrFN0R8AsvjRZX5C7PMB3aQ,14511
scipy/signal/_filter_design.py,sha256=xpd78pZOvbNd84agzgOuKaDHc1Nrals4phqHWITKtUw,202569
scipy/signal/_fir_filter_design.py,sha256=PXTWihYr7wgUW2CZ6K0akj548s2j86NCFYrHFO-yQAk,58712
scipy/signal/_lti_conversion.py,sha256=ONy5p2yBrtT4VZuyVU5svUpSnbooK129LIVOjzyw29c,16672
scipy/signal/_ltisys.py,sha256=lG5tNTWwRiedyN947v9-qC5JqzQg6mGYn9wuetV4jsM,124574
scipy/signal/_max_len_seq.py,sha256=oVUkL3qSfM1_FDWY_AcUI5fFi2vBQH0f1iXiDVOrIbE,5199
scipy/signal/_max_len_seq_inner.cp313-win_amd64.dll.a,sha256=5ToatCpysqARNje7KwXD9MovmXzaP2e1c5abukorJZQ,1676
scipy/signal/_max_len_seq_inner.cp313-win_amd64.pyd,sha256=eJcEBQ5XsbwbAVqSHfzDwjkSCx6bbXanROHXrTTR5uY,1006592
scipy/signal/_peak_finding.py,sha256=gjjLiuiho1HNgze2IUeFsooxDYXk0RXU2DZ8QPDjvgI,50166
scipy/signal/_peak_finding_utils.cp313-win_amd64.dll.a,sha256=EgT1kBNT3re8_ZiObTokKwZ5FhHU2_yOYFPzQiZI_4Q,1688
scipy/signal/_peak_finding_utils.cp313-win_amd64.pyd,sha256=fNsGkli1p2MK-DdRWa4Q91tEjzyL61XEJhxFp08WyF8,158208
scipy/signal/_polyutils.py,sha256=Gp61mbWdpbmN8GZiMOy8lOH3lMY9wfnsl86yqFwHJQg,5538
scipy/signal/_savitzky_golay.py,sha256=iagtdjN0dhETVa7ekXTPkZqmgRK6ncfJViq__xDiL00,13804
scipy/signal/_short_time_fft.py,sha256=9o-sf5S2mEh3_VSBnd6QVas84YUvsxJIcjFstXJjGoA,101652
scipy/signal/_signal_api.py,sha256=vs_LriNfK104xHwqekotBDotzyptV8LmVL957afZk5o,1267
scipy/signal/_signaltools.py,sha256=MFwxfPVy-pOF1GeLVLD0bD18vzq8dNqzWKqI4NbmP0w,198046
scipy/signal/_sigtools.cp313-win_amd64.dll.a,sha256=HEHXW_cYMv4X2PxqLYE1aS20WFY1_d1bUYaEYOEYIJc,1568
scipy/signal/_sigtools.cp313-win_amd64.pyd,sha256=9Vc2C-QjN7L0tRytFXtQhB_IxpJD9G26cPOPGv2FQbE,130560
scipy/signal/_sosfilt.cp313-win_amd64.dll.a,sha256=08IWbmpXpUs9lPZT4OWWY-tgNPMFTPfWbyxLQ1Jhboc,1560
scipy/signal/_sosfilt.cp313-win_amd64.pyd,sha256=_k1fCot8r3b27aDQhe9JluWoINNMm1WNyrPO5iJ1RPE,158720
scipy/signal/_spectral_py.py,sha256=x663ndtMFi2_NGkU26tANzWyZ9tpHeg1RH2JH0b7_Mk,98524
scipy/signal/_spline.cp313-win_amd64.dll.a,sha256=M6HbhPprlgJV9Cg0ytfkNEu7n5XAnXAvq7XyKZcVanY,1544
scipy/signal/_spline.cp313-win_amd64.pyd,sha256=cUT7TQxhe3P8IQaDebgB_eopItn_QXi3bCFhlU3hc7g,63488
scipy/signal/_spline.pyi,sha256=GCkRUtUAxpa7e_6K0t6MtqFcwymDm-EMMfjXMHb3xjM,982
scipy/signal/_spline_filters.py,sha256=i3oA22bIWZm5GAwF5TnI6qAYGvtmKvbdzzKdhsfy1ZM,26376
scipy/signal/_support_alternative_backends.py,sha256=bPvCIrJuz1p20spZuNfJ4dBEPHT4UVNZ7-bGbZZKcNI,2577
scipy/signal/_upfirdn.py,sha256=6TrSYW10tQuuXbKkcrXfnn6my1GYsDaIjlguEnTPPN8,8195
scipy/signal/_upfirdn_apply.cp313-win_amd64.dll.a,sha256=7oa2TBIwByN5I2qQJ6lR2f1I23kmFeselyGyu8yE2JA,1628
scipy/signal/_upfirdn_apply.cp313-win_amd64.pyd,sha256=E_U7uvNPuwhKl7qW4YZTp1uw5JzgqWLSnysVHLH1MpE,237056
scipy/signal/_waveforms.py,sha256=5OGSDuuXnEgh3t6ZjXNFYtQcRD0UccsocuLXBTCKbM0,23599
scipy/signal/_wavelets.py,sha256=_BFZc21IxfxsotDidicXxBWlSQttNyOQ5WlyLEwQul8,915
scipy/signal/bsplines.py,sha256=loef-iAMQV1REr5EwbCNDST0FHLHgHnwS246fkgZnSc,672
scipy/signal/filter_design.py,sha256=N6PHV8A26WatoX80yg-0iFfuDC1-sPiyHs4FHsrhQ1Y,1140
scipy/signal/fir_filter_design.py,sha256=nhg1ixRXoGBdSi90QvHjcDPM_7RJnCcR2o8kIoD8_Dg,678
scipy/signal/lti_conversion.py,sha256=6wCVjM9xgyn3y5YrXT6aPFOUHSISDkNFaSFsVme8BSk,659
scipy/signal/ltisys.py,sha256=hA9Q6qciqgKHRLeUSOedGx9q9jN0dWyERJ3F19_ia_s,894
scipy/signal/signaltools.py,sha256=Ia88ONqXpX4qZyYd364I2CZQyqn2Mqwa5rCOihEhDzc,1065
scipy/signal/spectral.py,sha256=Xf30DdZVzd-sp4GIPvCiyulUJIYNo-x7LxeqlyQLQIE,683
scipy/signal/spline.py,sha256=6TJsW1oNVX9smMcykJ9p_UaZM3FG4l2BFR2q2Kzc-Jo,554
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-313.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_splines.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-313.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-313.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=8ByUbVby3ef2XYHpVVJ0gkXloeSE98RK-nOcCWW8RU0,13059
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=vdpsiDEwAiyI76Z1cHjTy4Qo8mVJvWY01oy6Et6q9Iw,3700
scipy/signal/tests/test_bsplines.py,sha256=GGj6C2j613StCBRtYg-9ehqLb8IQyFbPZsa1aK2FeN4,18200
scipy/signal/tests/test_cont2discrete.py,sha256=lYqcPRnQbjXfDzj4N3Zdiu2Ka1eAn6fABLqxlmUsp50,15267
scipy/signal/tests/test_czt.py,sha256=uihNKvHxZWJE__J36TMtE-9Zo-w2nMM-ZB9Ns7P8658,7377
scipy/signal/tests/test_dltisys.py,sha256=okDw2DtC6EVroj8WDcrhUECXnNhocuBKGbzi_UdWML8,22082
scipy/signal/tests/test_filter_design.py,sha256=_aXV-ddS65KqsimUBbaJa851R6E2CUnOmPwfPI7NIA4,213439
scipy/signal/tests/test_fir_filter_design.py,sha256=u32o3oOvS_Nx12-unDWQMHaeUZmyMFOHXgdqWsTs40Y,36451
scipy/signal/tests/test_ltisys.py,sha256=a2XgQz9UBkOHhCthMu35E3V81xr3JuPiNAJnwFMCEzM,46295
scipy/signal/tests/test_max_len_seq.py,sha256=0nu9ZP7BzyaUQKFIacGAtyYDTZYNV1X3mFRTTJmXrLs,3389
scipy/signal/tests/test_peak_finding.py,sha256=YG5-lneGD3P7pdg0xtgAIJTsCR1OyaVMeV6DiPgb_Xk,36991
scipy/signal/tests/test_result_type.py,sha256=yPIKjgOsG8al3TkceA7uoe9N24cFTM075sV8WVY_b9Q,1624
scipy/signal/tests/test_savitzky_golay.py,sha256=bV_jKPuTxl1vslb9XyR9WKEcXtNNB0EkiZ7oyVTdZSY,12833
scipy/signal/tests/test_short_time_fft.py,sha256=FaWjZNxHZUMl8CrR6ARZqe4s9ZwIRrxyoRURFxV7zb4,48574
scipy/signal/tests/test_signaltools.py,sha256=Xgxb8X_mmN3rCLQfHGk9eXcY-E2ut_dlHVO1DVDvHI4,195545
scipy/signal/tests/test_spectral.py,sha256=LyQXg_yJuQA-HciIMLqD3jcw9k1m6jalmjWcObuiA18,82617
scipy/signal/tests/test_splines.py,sha256=GQBSfl7BrrheWmU2E-2tNHDxkQgktx96UZ76Gu7W6g0,17505
scipy/signal/tests/test_upfirdn.py,sha256=_VzgMMyOY3wvR-iBDnuw8JfnjzqJ-QJ31w3s0bce-Zg,12924
scipy/signal/tests/test_waveforms.py,sha256=oYqBVDVCy5RfOKk5tpms39t7u1ISeIsReusUy9QQLsM,13954
scipy/signal/tests/test_wavelets.py,sha256=JYyICY7OMUBkmQ2GMzReqeXqqfM960q9rsDat_R2pgw,2204
scipy/signal/tests/test_windows.py,sha256=3peFVTNm3WTps6zvMgKG6S7pWk0kxjN5DNowXQWASzw,51576
scipy/signal/waveforms.py,sha256=GIzJ60y5sd1PeYWCEHRJeyqb_EKLLKojXjluMM7juvI,619
scipy/signal/wavelets.py,sha256=RxHaW1yBGGnrA6sMHc9ryMTnf8Q0IMvDuPAgqaxBe7g,527
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-313.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-313.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-313.pyc,,
scipy/signal/windows/_windows.py,sha256=xOd6gK_osW7V7h-yrUTjY00c6vAh3eKib8IZW8EbDZM,92014
scipy/signal/windows/windows.py,sha256=7yfP6swdGZdTI7N37_Gg8PWQz0-HyYqMkfWqAHwiVTg,862
scipy/sparse/__init__.py,sha256=1IkxjTnOy8YW7lzb0aSviwRvYU7WA-zZSZLblta0Iig,10300
scipy/sparse/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/__pycache__/_base.cpython-313.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-313.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-313.pyc,,
scipy/sparse/__pycache__/_construct.cpython-313.pyc,,
scipy/sparse/__pycache__/_coo.cpython-313.pyc,,
scipy/sparse/__pycache__/_csc.cpython-313.pyc,,
scipy/sparse/__pycache__/_csr.cpython-313.pyc,,
scipy/sparse/__pycache__/_data.cpython-313.pyc,,
scipy/sparse/__pycache__/_dia.cpython-313.pyc,,
scipy/sparse/__pycache__/_dok.cpython-313.pyc,,
scipy/sparse/__pycache__/_extract.cpython-313.pyc,,
scipy/sparse/__pycache__/_index.cpython-313.pyc,,
scipy/sparse/__pycache__/_lil.cpython-313.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-313.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-313.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-313.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-313.pyc,,
scipy/sparse/__pycache__/base.cpython-313.pyc,,
scipy/sparse/__pycache__/bsr.cpython-313.pyc,,
scipy/sparse/__pycache__/compressed.cpython-313.pyc,,
scipy/sparse/__pycache__/construct.cpython-313.pyc,,
scipy/sparse/__pycache__/coo.cpython-313.pyc,,
scipy/sparse/__pycache__/csc.cpython-313.pyc,,
scipy/sparse/__pycache__/csr.cpython-313.pyc,,
scipy/sparse/__pycache__/data.cpython-313.pyc,,
scipy/sparse/__pycache__/dia.cpython-313.pyc,,
scipy/sparse/__pycache__/dok.cpython-313.pyc,,
scipy/sparse/__pycache__/extract.cpython-313.pyc,,
scipy/sparse/__pycache__/lil.cpython-313.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-313.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-313.pyc,,
scipy/sparse/__pycache__/sputils.cpython-313.pyc,,
scipy/sparse/_base.py,sha256=LxS58TXRnW9xjxwJw4Bc9-SjXuP_FeX3Z7bZFzkgN8I,60056
scipy/sparse/_bsr.py,sha256=TK4N71JC0L9-ez6qNWjgWWmqiq2KMJ8L_7m7likwvj4,31837
scipy/sparse/_compressed.py,sha256=C_-MljUvD5LxygVQbXSvWgYtwEI19UjILd1mB122tg0,53001
scipy/sparse/_construct.py,sha256=shsI-BcMCvSIrhA7iKcXyoYI0QizE_XuU42SF0dd94g,51074
scipy/sparse/_coo.py,sha256=J74b4AJpewrvNM1RrdnDCzW9MhY-yoSEKVrM88M4uJI,63099
scipy/sparse/_csc.py,sha256=Oj-uQdA_vcAJFgXg-Dnq9yCsllwr_j660rAr326UwVU,11505
scipy/sparse/_csparsetools.cp313-win_amd64.dll.a,sha256=FTZTXgXp0M6gL3w88usXRLISRDCmYcZzddk68oF9K2Y,1616
scipy/sparse/_csparsetools.cp313-win_amd64.pyd,sha256=0Eb2dnXv98EAysGUalXBsXWaXQoHWcy4tSOLAiBRpjE,527360
scipy/sparse/_csr.py,sha256=2WMThlUg8RpIM5FlwRzFubLnUSeBzEXtRADQqn17ljE,18710
scipy/sparse/_data.py,sha256=43mI2sk1AbSsAm3fNuWtZ_H7omJEc2b3sekNjsxnwTw,21539
scipy/sparse/_dia.py,sha256=M_2gZ8qDcfnJkJIcrrb44EYd752Ees101kJjP-WCOJk,24591
scipy/sparse/_dok.py,sha256=wEPc4BCcQA8W0iTQAKMh4lX4auPl4nfi4wN0sGALVkg,22920
scipy/sparse/_extract.py,sha256=9_Hbn7ytKL_Sn8ZT1ElWU1fgvLNf1jxa2BJGK_3sVDQ,5236
scipy/sparse/_index.py,sha256=N-4k2cYO3gv1BD0eqIHFVnnMenJ1SVtPEgGK7o64B50,16812
scipy/sparse/_lil.py,sha256=B2m0jN98nzHdO61-znne1yEzlUoL44HKM3U7YcsVZa0,21757
scipy/sparse/_matrix.py,sha256=uKg7D0F_bO7zBXZZ47ugoerFn9jH1Y11G5BY1AsciaA,5191
scipy/sparse/_matrix_io.py,sha256=Wr98d8p_VaifuZKm8RNQAFOgHoGmI7DAfr82BxV9uME,6127
scipy/sparse/_sparsetools.cp313-win_amd64.dll.a,sha256=E5FvmzMVr45R9nf3Mq5ppjxAQiJRcYtb-XzHmQQ98ZA,1608
scipy/sparse/_sparsetools.cp313-win_amd64.pyd,sha256=87_E669GufrAu1d9tJJSrtBWh_3SqmKKpyui6e4pXKI,4107776
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=4dmY2td7JWfiXW1K6OWemCf3OussbApwDF012pzpOnc,21727
scipy/sparse/base.py,sha256=j9bEsvP_twG_LMpaVNMUHO79UpnTRXnEkbmon2F4Y5Q,633
scipy/sparse/bsr.py,sha256=jbv0PChEwFcTN25g8GPdjuFlxLffooR4FGiR1Xga-vI,583
scipy/sparse/compressed.py,sha256=jaf27Kr72YQ8kCDqa4gLdA8VUpjM5OUhSBLRIW3VsLY,570
scipy/sparse/construct.py,sha256=PpGMgZRxg0kHwM6ejPBSJkQLfgrJUdIs0kNcPMk4FqA,850
scipy/sparse/coo.py,sha256=eFfj9Z5PJ5RmCQB8t11__2W0ZEc2edoDs0sAG1LS7ds,615
scipy/sparse/csc.py,sha256=OOISSsaNsKp4WFdzIuOg-r4o2EJYAotsi8YAXciqCio,583
scipy/sparse/csgraph/__init__.py,sha256=ErtX2spmCtvJdiVYtH19oi3mw8TSK4lHsnE4RleCODU,8052
scipy/sparse/csgraph/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-313.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-313.pyc,,
scipy/sparse/csgraph/_flow.cp313-win_amd64.dll.a,sha256=jKc3at83dmJEVe1A_5GewazSCrbKPIfMPl3skZyG54I,1520
scipy/sparse/csgraph/_flow.cp313-win_amd64.pyd,sha256=LNd0yqe_g2xW5ud2iyAoY-UXJXNu3F-yVV_P5aKq0Yo,194048
scipy/sparse/csgraph/_laplacian.py,sha256=-qgkmuqfOsQfMjF2mvjpXJZ4SFOl71_Mle_6km6Rmag,18836
scipy/sparse/csgraph/_matching.cp313-win_amd64.dll.a,sha256=hJrg8VNn8dhBcmhWQz_MOQ9Psjgs2kSguvRYNLdy0Ts,1568
scipy/sparse/csgraph/_matching.cp313-win_amd64.pyd,sha256=Dhuk0v5URHlxGDQnL_YSuILzw5A-BgHGWMNR5idDw38,201728
scipy/sparse/csgraph/_min_spanning_tree.cp313-win_amd64.dll.a,sha256=HXGPF6mVxrAz_tiD07manMdlc7rAMnsIR5MNvWncCOQ,1676
scipy/sparse/csgraph/_min_spanning_tree.cp313-win_amd64.pyd,sha256=sHTIIs_4_8omGyj3FZKC4w4YIVWiKeTBvAiSU7pLajY,117760
scipy/sparse/csgraph/_reordering.cp313-win_amd64.dll.a,sha256=0zMJFvXhaYfTmt8bG3a5aG0cgkcL-G1JI7zaVzk-NmY,1592
scipy/sparse/csgraph/_reordering.cp313-win_amd64.pyd,sha256=94nbolH63NYoqPgxn8amKFrROBmo5su3frJBresGTgs,176128
scipy/sparse/csgraph/_shortest_path.cp313-win_amd64.dll.a,sha256=fWiQAFfsUhElmiY4bSQIvAThl-Bxse6BaBX0tKQoR-Q,1628
scipy/sparse/csgraph/_shortest_path.cp313-win_amd64.pyd,sha256=A6RgFjy5Ee5B-6SPfoQ6VEfEbV4Jo7Rhf0qj2WYtUdM,579072
scipy/sparse/csgraph/_tools.cp313-win_amd64.dll.a,sha256=_RH_UnyjhynM-sqK_lKBXWRf5jfxVypM9N62Qms6r1Y,1532
scipy/sparse/csgraph/_tools.cp313-win_amd64.pyd,sha256=fk7_1hXRypN-TvhIRLDZM5qmSB0ZYzs0ykSeFH1yrBM,171008
scipy/sparse/csgraph/_traversal.cp313-win_amd64.dll.a,sha256=3wORwpcNwrpxwhlLHo388zcQY78BL-vcE3fh57RfJyI,1580
scipy/sparse/csgraph/_traversal.cp313-win_amd64.pyd,sha256=v9Jb0cgaREjUhhXDushw0DWmYntEYaaWBO_BMRrdfv0,463360
scipy/sparse/csgraph/_validation.py,sha256=TupvEfqEDYz97SMxP_EO658lWBJH31G3W6ViP7fZIJI,2695
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_pydata_sparse.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-313.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-313.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=IxS7e33rMTMiBQDBvsT0-BEgQTWHSxi4Ei20aZggUpI,1915
scipy/sparse/csgraph/tests/test_flow.py,sha256=vEXctFogFpSrSm_NMh4XqamdrrzdhZxXvW_Zw8Z2LSI,7927
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=N9WMTrys6E4FHisuuVziu-ofW7wzgIxl4jhOu5dIMZs,11304
scipy/sparse/csgraph/tests/test_matching.py,sha256=8Jr6MntwIIcJMTZCBkwQi67ZLxGsVRWyRe5_XGIphsk,12701
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=7xQsdWdNPRnw8eYa1TJUPk5zRlkVnN1nBPCW-1waYI8,5164
scipy/sparse/csgraph/tests/test_reordering.py,sha256=78ZmoeqkNB4a6zyFV37IAo-f-R48gPe8G9g8mF-spls,2639
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=o12CpkA2h2-exGbFWrJu7gNUTPx_37BngLOFGMgaoTc,19024
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=vOPVZpD7U8Whhint2LmT-vejC-RduRaVCzqX2X-r1mU,2230
scipy/sparse/csgraph/tests/test_traversal.py,sha256=M_C0cRNv3VSbb8uuFKqOODBCfBNs5wf26Nri59UldZg,6297
scipy/sparse/csr.py,sha256=KW3YHvzaVcCQsWDIXOK1PDPWcSnWvuxyM-7lCU6B0zc,583
scipy/sparse/data.py,sha256=OF22VaoSaJXqgt7YHzphdp8xPEfd7HXVqhCM2PT74KE,522
scipy/sparse/dia.py,sha256=E492Uk6wWwdZS5IZ2GIM-s9gtpYBGzcDPxsgAf1kkSk,583
scipy/sparse/dok.py,sha256=6KYBAYNsu9d4mJ1b8SdiLHB6x6FVYHGX2Gaxf23cCAc,583
scipy/sparse/extract.py,sha256=tcwXkf3fvxKg4PotXMrvrjeMzCgqvUQBbDBzrKdLEJo,590
scipy/sparse/lil.py,sha256=mudAFHN3Tk2eQS1cWTQRAQAXGuPnikOtYWNWYRvwQqs,584
scipy/sparse/linalg/__init__.py,sha256=9nVFnMPzPtn1K0uhxyJoG9JP25kj0FVrcSkU9czesfI,4259
scipy/sparse/linalg/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-313.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=Bb2z-gERMui5XZRbT6tOjUJbTLk1v_l2hLFWR7yyJYM,2110
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=2rzAsN26FhpV4gxSyCeN1YXb3meVm_cx5bx48XNh1p4,3890
scipy/sparse/linalg/_dsolve/_superlu.cp313-win_amd64.dll.a,sha256=fG0jAF7Pqr7eR-wenPK81IHnAjCxiBjLiYJQEM_aCxI,1560
scipy/sparse/linalg/_dsolve/_superlu.cp313-win_amd64.pyd,sha256=yRbs9oVCVhKtGJVehSAOPm7ON3IonKnURMNsHKNDKm4,542208
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=Sc_b2leAnlPpDiG4ohRhnKeQUWJuob45-SqtKkZqFsU,32059
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-313.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=5nj-lEJ5nMgAo1VUxRtUsHZdxEwkZtMrdbgrf-X7lhE,34141
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=Qm7n8nD_ZBwNkURq6rv6i9Ui96tVi8Re-O0i_aMpYNQ,20448
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=QXVUkudMCA3Br7Tgg1oLqdz4SGbxudZuiGt79dmWw9U,15385
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp313-win_amd64.dll.a,sha256=cdMNeP9A8IUwXq1IYgkjsna5vPkY1vVMvKNl3xXxp9A,1544
scipy/sparse/linalg/_eigen/arpack/_arpack.cp313-win_amd64.pyd,sha256=IC0_xCtjO3NqbiAv_seiFVjvQheCY05iqmtl8kEyxLY,926720
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=RxWKM0KiyUmcOXKQq9txvxEWv8bIm8zHtXfI27iBmJo,68992
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=lbDDNeiT_FPNGptjaYuJ--0dfA6OqIcjeL8dYP3pWLg,24452
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=lIh8AAqSCOic0gqv9-LaVFJjcTRD2AvYQ11TIc16Wgk,43077
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=AgCm_YIFsCP2bqJN9inRJJddPaKR5iIbw_WCiPn5vHM,28146
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-313.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=XVjdEe584u65fLWGDnSW5JFBQ1_pP49qBayvAETvR-o,37680
scipy/sparse/linalg/_expm_multiply.py,sha256=b8eIKs9PHlSN0jxdtsjohPL4jeRIVM7B_xc-JmdgKk8,27307
scipy/sparse/linalg/_interface.py,sha256=PgnyG9ayK2Q2Sky87Q5JQ3TGZngnUkIjePZEVrMIaNM,30383
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=QkP2X3lZkIG1hoCyz_DkdBoL8CAi0DscVqpF4MNNKI4,16249
scipy/sparse/linalg/_isolve/iterative.py,sha256=GJc_oWbSPMTMlMxQNJn_IucJ1BKB990tWl89n2dGR-w,34474
scipy/sparse/linalg/_isolve/lgmres.py,sha256=rtZ02BOpFOX0OGANng2dXe0dvVls5dym3-E9q6nmPpc,8853
scipy/sparse/linalg/_isolve/lsmr.py,sha256=8eYL4_Uhb2Ar8zU1rMckdKqA--AUnVkpFF0EMrwp4M4,16136
scipy/sparse/linalg/_isolve/lsqr.py,sha256=UzxSqzKZi77rfa8a5PGvjd0slyshLbRaEQQ1-6gQ6NE,21911
scipy/sparse/linalg/_isolve/minres.py,sha256=JNMZIwoOUaSfPktUly2BxizxTPVwIQno3pbsCOHJZl4,11234
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-313.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=N0gqodK3NSLtjdeBsBbVQps-SsdAtxBhyxPYK2N8mWw,6044
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=7NkhXoHl5cmuOxM2oP2JZ-LxM9d5zZnDrVaUxPo4kW4,26990
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=65KcnvybKHqmcJVXVTvQjzcEXMQ5C_2W9vGJAxRkWOo,7673
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=jEw11X6oBX6fObpWX2ZTMNzCiIwxlVRHPuK9m1XDl6c,6547
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=i9aWA_Lxjz4N8TDbQ1TZAZAfDL4CKcmW1g3ktvRMkFQ,3879
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=tom4nzeIpvusT1NhxZODOkZ25jqRfsgAmd0rWDXSg6s,2531
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=W_ERpPV4ZfxThvgBHxuyhiTBmmfSbQKFFZaSK25mGBg,274
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=wQc9j7m0MLsr_d5Y2eLf-SewPnrDK-3_1O7lmkEA9tw,6340
scipy/sparse/linalg/_isolve/utils.py,sha256=BFF2nAagktrQEVDBCP8EPI3hxSAXCfPLc0-IZThajdE,3508
scipy/sparse/linalg/_matfuncs.py,sha256=PUghg3WrhtMONh5nU2WPuZTCuw3DkuBWn7cLbSewdqI,30278
scipy/sparse/linalg/_norm.py,sha256=93-LHVDpKt2Xjk7Idtcweu7tllGP2PoNVy3XbwpaAjM,6366
scipy/sparse/linalg/_onenormest.py,sha256=y1HJKNEm2nZCKw-cNDoquk53NZoF1u1PWlN8SB70DDo,15947
scipy/sparse/linalg/_propack/_cpropack.cp313-win_amd64.dll.a,sha256=z3kUAZ1AleSARluWj4qkwIpeyR93DB9m5O1AoflQgw8,1568
scipy/sparse/linalg/_propack/_cpropack.cp313-win_amd64.pyd,sha256=zclEiPLSK5k6bpeCWiQ6qWlbdd4dX4eJOGm3Ibk9bqM,605184
scipy/sparse/linalg/_propack/_dpropack.cp313-win_amd64.dll.a,sha256=0WCGtCIil_0A7SgRcLgxuMQFBP1PmhOa_tZ9Zue94zc,1568
scipy/sparse/linalg/_propack/_dpropack.cp313-win_amd64.pyd,sha256=o_a-m1eQWs3kSIQY2GIU0224M0FgPWHEP2DNczseeYg,573952
scipy/sparse/linalg/_propack/_spropack.cp313-win_amd64.dll.a,sha256=M8qr5qTNXNjpIjtZQNbbi_nZIdT4U0wNur1fh1KCjL0,1568
scipy/sparse/linalg/_propack/_spropack.cp313-win_amd64.pyd,sha256=sq2JMQ-095myOPf46kS-p_koGTzTS5fs30_8ctYyslQ,575488
scipy/sparse/linalg/_propack/_zpropack.cp313-win_amd64.dll.a,sha256=ZiIrdc45i0ZJFud_dI9SR4s2LK7bxnmr6607-C9NhBE,1568
scipy/sparse/linalg/_propack/_zpropack.cp313-win_amd64.pyd,sha256=zB5L105hc7zSLtQwBidiRZeVtmhcGaGbtZ7Oiftoko8,594944
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=0sodh7-ggYZjaeTBwsi_KYxS_WLZwLkqvyTiqXzhH0M,35188
scipy/sparse/linalg/_svdp.py,sha256=JuOD5fJ0nTGzCMYeQCBLcHGjOHX0tAE7FlirtqY0U-w,11509
scipy/sparse/linalg/dsolve.py,sha256=FqCBojSMWKfXKL8U2Kt-uhTQjy2bFRK4jdm-0uvmcLQ,676
scipy/sparse/linalg/eigen.py,sha256=RdD87vomwamZfWiKVcxXX7lgI0on79M2K_jzU9wmr7k,647
scipy/sparse/linalg/interface.py,sha256=pUQ396Go80oD5Nhnw1ORA0ug3ilOEJ00tar6czQ4JVw,593
scipy/sparse/linalg/isolve.py,sha256=ra9gIhO5qfFPMKsW8OgQSf4QepdykZpTUMaXXLakEOY,671
scipy/sparse/linalg/matfuncs.py,sha256=414icIriQbA2Mwro2TI1MMkStV6F1fRi9Hhs9mrpsBk,588
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-313.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-313.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=btt1PXGw7k0X12ksf4nBbbajIOi_DPpoDeQvZvoY9tc,15212
scipy/sparse/linalg/tests/test_interface.py,sha256=oBESVRYjWxRhk3s0kjbshA_XD-sVzAYoctkCnD7UOVs,21647
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=TCkyKYiOw9JSrzVcrreDz7fDZIpzU2l2N7kg92nZL58,22332
scipy/sparse/linalg/tests/test_norm.py,sha256=ssry1tYOW3Rf0OnwWSV4PYoAi6ANm6TAq12yLmAlR_k,6870
scipy/sparse/linalg/tests/test_onenormest.py,sha256=eJtPniHhjmtVcq_f53k6orimucPJ422vbEi5FocRigs,9504
scipy/sparse/linalg/tests/test_propack.py,sha256=QVfGQXgAQoo83yNvc2YMfrpp3zLC3P7x2RN9p4gYFwk,5732
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=EIGXFKCH_rXxs9zdfoY6HJ9gjcitFxlO5HGbSLaAEwI,7574
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=a3CKlj_vI_A45sZgx-36usRCswvQNeutrX0hRK9bkzk,13191
scipy/sparse/sparsetools.py,sha256=sKV6diNaH4r4B1BV-F-1AvuGAHw2IpyraSJlBoXZ9YY,533
scipy/sparse/spfuncs.py,sha256=FK1cecfhjHLk2_gSAoTSsved5HgcOwUPrtNSnG7f2_o,525
scipy/sparse/sputils.py,sha256=ckuUej4jR09MrvynJTZ9c6_QCKTHn-7ZUeBcBf6MUzs,525
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_arithmetic1d.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_common1d.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_coo.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_dok.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_indexing1d.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_minmax1d.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-313.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-313.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_arithmetic1d.py,sha256=oyrqz5ltKnld7aupyr5KtbZJbIFz3vQjcMCzyCZ_VhY,12325
scipy/sparse/tests/test_array_api.py,sha256=U56C4NdY9P3HVoxbS57IR-M4l8U-fZluYPIcIRV_Dfw,14762
scipy/sparse/tests/test_base.py,sha256=e2Uh1Jy-_BlE3-P72Pwc5CH9q883mgUqRlqIDqeMY7E,226182
scipy/sparse/tests/test_common1d.py,sha256=czj_jPydcZWBBC108qJW-OqHcgJhOxwFCwL53IfOH9I,15918
scipy/sparse/tests/test_construct.py,sha256=hhw444SBv1zoVkNyeSGzq1Gz4JPaEhqhS91q9wAcF3M,39306
scipy/sparse/tests/test_coo.py,sha256=Qm3qWR_V3hfwCRzBo8W0MYewUDklePkV9uv1KxVai_8,40751
scipy/sparse/tests/test_csc.py,sha256=ak1_Ka7itovqPa9NltnRgnMx_yvjNNw6sFsPaISZZyU,3056
scipy/sparse/tests/test_csr.py,sha256=WoddbHT0VvwQ7tB3XxDCfGFiL4Bo89vXoOP2YXmyYzc,7837
scipy/sparse/tests/test_dok.py,sha256=cpmX7WKvbFIhO41I1tNcNPm9GC1ToFCz-SXvDUPg_sM,6168
scipy/sparse/tests/test_extract.py,sha256=NJIEcflkpb-k0llvH_6uJMAoaysWliWyi4bRjl0NhN8,1736
scipy/sparse/tests/test_indexing1d.py,sha256=cDL0drcXj7TsCaSam0Wbw4F51LBq1t1QX8KdiFikQ-E,21357
scipy/sparse/tests/test_matrix_io.py,sha256=7SOFHH8giia5Xdm5QmNuu1Sr5rLYtGF916iQYPqIsJU,3414
scipy/sparse/tests/test_minmax1d.py,sha256=Ceacg9C5H_qMXEsiLrxpwzUeDUyhivEYCKlNGANwk7Y,4397
scipy/sparse/tests/test_sparsetools.py,sha256=cNPPioCYVyPtv5n35HLAQS05dsIX5pNQog0Zcd-tdHc,11113
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=oHSPymzr8-IIq04UOKDvcDv_AbRJMsM_hwKi28pbWKs,16910
scipy/spatial/__init__.py,sha256=poHN9MOFpQpE9zL8GT-iYH6TrV_yf5qgzQASLhz2rFw,3860
scipy/spatial/__pycache__/__init__.cpython-313.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-313.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-313.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-313.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-313.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-313.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-313.pyc,,
scipy/spatial/__pycache__/distance.cpython-313.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-313.pyc,,
scipy/spatial/__pycache__/qhull.cpython-313.pyc,,
scipy/spatial/_ckdtree.cp313-win_amd64.dll.a,sha256=E52_T84Y2k_xCkzklQPUe8H2wT07fNuxk2OX-Mnw5tw,1560
scipy/spatial/_ckdtree.cp313-win_amd64.pyd,sha256=oHktkwlcBppVb883elBD6hqLZBFs77jDrp_4YfxcSEw,1570816
scipy/spatial/_distance_pybind.cp313-win_amd64.dll.a,sha256=3qjybYEjttefBtrGDQ1VaBHY4v8sUEf0IBDkO8ofcTM,1656
scipy/spatial/_distance_pybind.cp313-win_amd64.pyd,sha256=IVraIO0PtxeDRuHRd8NAHYO0C-kQXQzRZgkuJmKtTUw,1390080
scipy/spatial/_distance_wrap.cp313-win_amd64.dll.a,sha256=G8yWgVz4BfNvcAt_XUxLXFKgaC5nd_f3XMMw8tp4xnM,1628
scipy/spatial/_distance_wrap.cp313-win_amd64.pyd,sha256=EEfX2on05LPdenHeERiQZwYxzO5asDRccTyxSYTSi_w,111104
scipy/spatial/_geometric_slerp.py,sha256=QDtTuk_oZ5t7_v3Qjnzb4pMYmkC_9Yd_-7rNXRsTZBE,8189
scipy/spatial/_hausdorff.cp313-win_amd64.dll.a,sha256=yE8INxgLz7dSaF9pibHW1ipRWYbXmueD_NuR9Y_oTSY,1580
scipy/spatial/_hausdorff.cp313-win_amd64.pyd,sha256=paU109GP71-hxbq_GFaIaYoCKxcNJe_Bt2k9O6WMzVo,99840
scipy/spatial/_kdtree.py,sha256=KMN1TaeHvhws_c2WBGLjrTMPGSMmcZaoEKjSKwaBhv0,34399
scipy/spatial/_plotutils.py,sha256=jF2xcCqATYsYvcU9mT3TrSWxFa_0wFYMYt1-Q7LXzt4,7831
scipy/spatial/_procrustes.py,sha256=7C9v3o_Any1L7SNCpE71iLJaV8nXUZfpvyEC7Mu4i78,4559
scipy/spatial/_qhull.cp313-win_amd64.dll.a,sha256=Injt8dewq0rVE5u4wjT-iBOfPu9VqAPNEaesKKO3BlE,1532
scipy/spatial/_qhull.cp313-win_amd64.pyd,sha256=BbE1QMbDwYmXnp8s2cNiOsIpslUz4pq1Uv3cw_v-_ts,891392
scipy/spatial/_qhull.pyi,sha256=L06jd8pQcSB-DcRMJe8wq0XvlIEzuOrZQAffq4zD7xU,6182
scipy/spatial/_spherical_voronoi.py,sha256=GC2l8QKNKwkg3Zvsi36jaJuEOAYQb16B6z0Gn1kEe_s,13918
scipy/spatial/_voronoi.cp313-win_amd64.dll.a,sha256=yWUwYUF8ui96wTwrggK2z0nqmmYN-fIdeg981Ek7cdY,1560
scipy/spatial/_voronoi.cp313-win_amd64.pyd,sha256=GV57MQmGPfNdYyLA7I73VLLSKGnXueEABpRr-WF5mBg,91136
scipy/spatial/_voronoi.pyi,sha256=gaEmdjWgHeIA9-D6tYXwimJCppJzgc6yc-sfLju9Vyc,130
scipy/spatial/ckdtree.py,sha256=Opsyz6WpYiV-27W5GJdfOWQTkQToWndreEx4GnwT2qM,541
scipy/spatial/distance.py,sha256=wrfkKDyuLLJ0ZenZeNVjrQ8AxloXUBoBeygknxXzH1c,101392
scipy/spatial/distance.pyi,sha256=InigXoZyyknOyRQRs8zs2_7PpSO8fho0abGrWThBhlY,5448
scipy/spatial/kdtree.py,sha256=zc4Kg-a7EvN2hXJq6HuCdPl6ccEbg5GyDV67Xq5nsiA,661
scipy/spatial/qhull.py,sha256=10n1eDcF9qrUmGpqrEb8sLjw_cXfSSgT_WU0-z4ucU4,647
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-313.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-313.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=n9OCDDgipsHJy2le-P-8fcS6PD8IQ33zmiz2ZAzbslY,3905
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=9YFOBJoI09uuGy5QvmL8Jx_Pl3DGCX710hF-HGqjsX8,90719
scipy/spatial/tests/test_hausdorff.py,sha256=FThkMuJ8XbVXaGhdAiesq5AZhmnWVsCVuarhNOLsfDE,8416
scipy/spatial/tests/test_kdtree.py,sha256=reHiQ-1e9OILf-F9MQYHfrBk3wzpR5C_PcG1adRcbs0,50876
scipy/spatial/tests/test_qhull.py,sha256=eJ5C5kQA-vTRtKfwd5lzQinFEjKGWq5FYD_cK94yhto,51496
scipy/spatial/tests/test_slerp.py,sha256=eS6DagFFbcWqHi3FJJ4zZ4GV7KkvVVf7jkBGbRgOgJ0,16844
scipy/spatial/tests/test_spherical_voronoi.py,sha256=nT88CZzq_W8sPdejyi_2OT8A8b7Yw8qsA7dTZqG61GI,14858
scipy/spatial/transform/__init__.py,sha256=DhyHvxJ9zxvx4FLOYjbZ0D-Rv6qgAJAOE714GXluTS8,857
scipy/spatial/transform/__pycache__/__init__.cpython-313.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-313.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-313.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-313.pyc,,
scipy/spatial/transform/_rigid_transform.cp313-win_amd64.dll.a,sha256=VqgjHRlDfuUgL0VJdcjYbT5bCRuJxnhVY0TSkREQt2s,1656
scipy/spatial/transform/_rigid_transform.cp313-win_amd64.pyd,sha256=hWXgiwx5m2xxf68HX7tzVFV39CHBfg_Gk4Udptv7bKg,364544
scipy/spatial/transform/_rotation.cp313-win_amd64.dll.a,sha256=613g8ZQN1C_x7zlJBny4f0ngGoOCLggAMNOF086Z3MA,1568
scipy/spatial/transform/_rotation.cp313-win_amd64.pyd,sha256=VyshkgFuxuKPyOa1mAgrrLWbUdvnlomixPmpxF-yn-g,771584
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=weRkpZWCgFeu5q6Ow0Up0k1eJGI1XrApez5VpnrlSYw,14536
scipy/spatial/transform/rotation.py,sha256=ZpBp9OghIvAzY601Zb_9TawwzxRgS9ydRR5aNcboekg,577
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rigid_transform.cpython-313.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-313.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-313.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-313.pyc,,
scipy/spatial/transform/tests/test_rigid_transform.py,sha256=XFT1orA-T4tz37X5Hnkp_v8WHru0tgoQ3-0v0pgEgZI,46199
scipy/spatial/transform/tests/test_rotation.py,sha256=KTNwotPz_dIIO8RK0hlS0JlBy1niY1y_nkEDKvEm1bU,89695
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=uFuyxQ5sodTiDlNgzp6H70M_Qf-44cA4xvwAI3OAaMg,5721
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=XBxrJOx6iZoaYRNrsnLP7RbYGTubBc3H0O_gvzm3Yr4,5885
scipy/special/__init__.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=EbnqRpguUSziXSjiz4OtKkMf-3p8twkop2rIiwUT2OA,32962
scipy/special/__pycache__/__init__.cpython-313.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-313.pyc,,
scipy/special/__pycache__/_basic.cpython-313.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-313.pyc,,
scipy/special/__pycache__/_input_validation.cpython-313.pyc,,
scipy/special/__pycache__/_lambertw.cpython-313.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-313.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-313.pyc,,
scipy/special/__pycache__/_multiufuncs.cpython-313.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-313.pyc,,
scipy/special/__pycache__/_sf_error.cpython-313.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-313.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-313.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-313.pyc,,
scipy/special/__pycache__/_testutils.cpython-313.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-313.pyc,,
scipy/special/__pycache__/basic.cpython-313.pyc,,
scipy/special/__pycache__/orthogonal.cpython-313.pyc,,
scipy/special/__pycache__/sf_error.cpython-313.pyc,,
scipy/special/__pycache__/specfun.cpython-313.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-313.pyc,,
scipy/special/_add_newdocs.py,sha256=YJddEI_Foi5Ag3rA7pOLEwZ3UrzWbMS8Y1knvjja6Tg,282677
scipy/special/_basic.py,sha256=34RP21qsFQ86xT_B7NA24-eGJsr_myfdplkKpvTDLbo,115406
scipy/special/_comb.cp313-win_amd64.dll.a,sha256=PHwD9dAE4a51pcSEHjTVCGQTkzYJpPNmcEQO_sMJYMM,1520
scipy/special/_comb.cp313-win_amd64.pyd,sha256=J_gI8Acabfxga_PVZaYMXCTwC4XooW1Pyk340uxIh1M,46080
scipy/special/_ellip_harm.py,sha256=Km_A9XgXnYTleTtRuUzOYLxR8OEUtmiYJLYsRSJaSNI,5596
scipy/special/_ellip_harm_2.cp313-win_amd64.dll.a,sha256=ldGoxBZqYlGTMf0bF3xZvsgcQc-wWIQqaJb-SRiElMY,1616
scipy/special/_ellip_harm_2.cp313-win_amd64.pyd,sha256=5xipf-4yTQoOYHDjk_W7uTildEm_RpOaV2xgMnExinI,137728
scipy/special/_gufuncs.cp313-win_amd64.dll.a,sha256=fwKxRa-YdypCltuHuYClGqwy09fsP-Ka2xYUMn_f5PU,1560
scipy/special/_gufuncs.cp313-win_amd64.pyd,sha256=6ywMZS3I-_7EDhDwhMxTW95c03ITlkXSzBjnvkOg3Bw,1515520
scipy/special/_input_validation.py,sha256=RBX0p23Oizg6dybDSKCJGGdConXDNKyf40vVP-v0vag,491
scipy/special/_lambertw.py,sha256=IYmy0Ymjk-l7T84uHr8_OADgpsUPy1K4F17QGRwWXuE,4111
scipy/special/_logsumexp.py,sha256=Oz4NUegfKuVRLf5DDrwH8e3PNJo46cRWWiwmgwGRym4,15061
scipy/special/_mptestutils.py,sha256=LXBqtPgWzXvdCuQj9HKceYaSPw_Ud0Iud-UbxLH1ceI,14894
scipy/special/_multiufuncs.py,sha256=YznE9Mzihzn3wLfO6tguIBGQ6O60VcCIW7uA67JBJL0,19132
scipy/special/_orthogonal.py,sha256=nVn94ENSKI5EoWPTLFzGwYbD0xTMbrvFBMn0DI3KJWM,76822
scipy/special/_orthogonal.pyi,sha256=Dqc_FO8-ijH0sycnJ7NU-8U-SdN4sp58sIvToXb3h1A,8595
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/hyp2f1_data.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-313.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-313.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=hFFkqjUUSdwgkyulp-p_5LCMav3CHL_aDs_JbcQWl_M,4201
scipy/special/_precompute/hyp2f1_data.py,sha256=rK8obG72p-UmpdjSvhS6-xm4A4TRscsUNAMKOuKZW-Q,15191
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=f21NuMoJE1Di4MF2kZfd6b_wiwqml-uQPylWdpncK_Q,3755
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=4IHODoYYrAFKw8nlJWAPzrOduiHIMUILTJaqtA4jbq4,13210
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp313-win_amd64.dll.a,sha256=qjIVUpdSmrs9ByV4DSYTxVhljRZyjKQylWipmyD6Fx8,1560
scipy/special/_specfun.cp313-win_amd64.pyd,sha256=MAYZtomGSljgQQrBl48G0TgdD9Xd0vAEvFSynKfaU2E,299520
scipy/special/_special_ufuncs.cp313-win_amd64.dll.a,sha256=T2xjk54XCidGwlGt8gwue6SKcWWW_BVN2baCC4P4nkI,1640
scipy/special/_special_ufuncs.cp313-win_amd64.pyd,sha256=ac9tCsWR2lKsCy2vLdc1k0WYI5ZyiOnWSITHQfTxvLk,2284032
scipy/special/_spfun_stats.py,sha256=Zb72pfmi5hzvkRXD2QXhVnJMmYyIjoqrg6tVxaCcoSM,3885
scipy/special/_spherical_bessel.py,sha256=NSnP6hv0cKu9H3NS0aRH0yeEKhznGQxgpF8SrEd8kyw,12854
scipy/special/_support_alternative_backends.py,sha256=DJns5XBKvK0owHa-nNI0il7eP5eITPHoE1jye7SRcx0,11015
scipy/special/_test_internal.cp313-win_amd64.dll.a,sha256=yNkdjY04t_xA_e31jPNYDb4xdVCbZdVM0nN3RdIEBv4,1628
scipy/special/_test_internal.cp313-win_amd64.pyd,sha256=Jn6BhxXmbZNwW2zyLsAMgj0RF0cdTCcJ_vH27BDD4sw,108032
scipy/special/_test_internal.pyi,sha256=fFGF8biY5WYD8O8MlPyUJKS-K3FSuSc1IRv4M8lj7OY,403
scipy/special/_testutils.py,sha256=aUmhRa942P4tbhkEDicBI-AXf2UWdJ9t1FfX-tm9Ifc,12292
scipy/special/_ufuncs.cp313-win_amd64.dll.a,sha256=H6zcwfjpieKPLn3wGtAj0I6kTLwKyy42hBpIQgPyU9k,1544
scipy/special/_ufuncs.cp313-win_amd64.pyd,sha256=xH8Ce4veWBu9iFZDowBUweLYpU2SpA5T2rwERE0ia8g,1483264
scipy/special/_ufuncs.pyi,sha256=N5Vxg3JVyB-dnUtE5B71UianIF7UZ1wSflc_J-uStJ4,9381
scipy/special/_ufuncs.pyx,sha256=naXiveRmtVwsM3rstZI5MVceKoxEs8IKEgzOLGIbTNQ,572724
scipy/special/_ufuncs_cxx.cp313-win_amd64.dll.a,sha256=vW8S9syhF605ipYLUxchGIccGsrVGM_08lOT03dk0nE,1592
scipy/special/_ufuncs_cxx.cp313-win_amd64.pyd,sha256=poBXmBWv0khsAIxDBLwxuaYvXag3sfjcvN-1KKycAlY,2330624
scipy/special/_ufuncs_cxx.pxd,sha256=q01BtN76ys7iXreCTL_J_RUGMqVGNzty63r57hD968Y,5299
scipy/special/_ufuncs_cxx.pyx,sha256=6IbwbFe1E6WvWvHCol4mtSWUCPs1Un_S7xTYnbzVxsg,29223
scipy/special/_ufuncs_cxx_defs.h,sha256=UABZT41TW97fRrLYNbbKLCf2fHGsYcbeHV_mUcjj2JM,9142
scipy/special/_ufuncs_defs.h,sha256=UIM-I2AxVy-wpG4lpqtww6o7W472ZfO8LSeKEMxJ-eA,2933
scipy/special/add_newdocs.py,sha256=5i4vyh9UrnCUZ6zTcVmy0YhsbP6z1Mddxb7qrUdWwKE,451
scipy/special/basic.py,sha256=UVJGOHZCijP6H5Kiexi5U2R-CVqfUSCo4Rhv6sxb4bU,1669
scipy/special/cython_special.cp313-win_amd64.dll.a,sha256=IBUkBhRyqnrzVrJm8OQQNmpiszERLcmplTYeOtsMBcc,1628
scipy/special/cython_special.cp313-win_amd64.pyd,sha256=021i6ac45okHmZrnAPVUyKM-K91kr5U1ESBecAZfqH4,2984448
scipy/special/cython_special.pxd,sha256=aWP5B1L4U8BBpLQNY2xgo578lnbGNIay_7k40MlbExI,16641
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/orthogonal.py,sha256=YlmIcqtx79IX4UoUK7FgKuyOLfFCIXCYeFHAvSz4HiM,1769
scipy/special/sf_error.py,sha256=OhEWgiU5kk4QblZIhTAfAc0eU-mUKK_nTA3H-MHlJ68,593
scipy/special/specfun.py,sha256=7-MuCncWBe0xHf_iPWACck2cJ8uR7cyeoMFMD5PtXxw,612
scipy/special/spfun_stats.py,sha256=mLQQYMHf6QowpGShlLy4CDL-Zri1zwyEawpx1U_f51s,552
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_boost_ufuncs.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_cephes_intp_cast.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_extending.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_iv_ratio.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_legendre.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_log1mexp.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_specfun.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_ufunc_signatures.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-313.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-313.pyc,,
scipy/special/tests/_cython_examples/extending.pyx,sha256=FW_hJh5PYqGgpJGy3_J8LZjXf9XLMsMzDu7wbGxiyv8,304
scipy/special/tests/_cython_examples/meson.build,sha256=L3Esk0wu0hQxWnXI2Nlt5ocpA1tpiscgSq9I6_B11wk,844
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__pycache__/__init__.cpython-313.pyc,,
scipy/special/tests/data/boost.npz,sha256=9W9uqJTP5Z7aJ23carRakXSwFWEt5x_6RDvsI9_rAw8,1270643
scipy/special/tests/data/gsl.npz,sha256=X7Vf4jA3wye4vNm0C6X9KNosK2_NSzPBxGwR1EYmvag,51433
scipy/special/tests/data/local.npz,sha256=ykTwo3nEwxCbDrGbvE7t-kjbmxQ-IJaoNyuXTdwOEFY,203438
scipy/special/tests/test_basic.py,sha256=CQN4DyrQOXd2uFLAwMafYquudj1tkNazsP_HZzA3WzM,199689
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boost_ufuncs.py,sha256=vXiMozmLeK4oC3Fihcr7P8asYGqMg4B2cU4a7htsf04,2367
scipy/special/tests/test_boxcox.py,sha256=Mi2LR1O_Oc93R-5H7683VxSc22bqHssJgorQv3dDj8k,3239
scipy/special/tests/test_cdflib.py,sha256=LedalT0hCUSzpkfA6An45lmzuQ6HHdN8_vH1DhxPgpE,25245
scipy/special/tests/test_cdft_asymptotic.py,sha256=dsIl7h0WEtzX7Y3vCZSMEKpwb4EZoHvwL4j3BvRd8Ek,1490
scipy/special/tests/test_cephes_intp_cast.py,sha256=INhKthUJ4kV3FLmbWJJothJ7NHyBZJiRQgfPA81Jm2U,1158
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=v2z60n9yL_xE0uazXkYgGmPzN9CakgnwkWjdN0J_7dc,19491
scipy/special/tests/test_data.py,sha256=vdJIDHqqt5rKrq7LwjkzSd8YXbm4AH-xWnaS8HkdQ6I,30899
scipy/special/tests/test_dd.py,sha256=KObFRKa8cqUp9VOGBMln1UG1UtMZqSbW9fqFu9yTWKk,1606
scipy/special/tests/test_digamma.py,sha256=vBfs2G9WcIEP8VGpGaCsFlTD5g3XODAP7gsUEmAq6Ho,1427
scipy/special/tests/test_ellip_harm.py,sha256=gUbH1B2dBI2OkMzrAkNMCkuIzHpd3qDsXvpylpp6LCc,9913
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_extending.py,sha256=RmlvXeXppPP_d_pJdH_s5mQWitad7gKApXboha3mxg8,1212
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=3J82wbtJByjq3hOsyPHYpBRzvR-VC9tP18e4tNHOH3I,4593
scipy/special/tests/test_hyp2f1.py,sha256=v4QfC4gHFCPB8hZtJs0Jh2LheIOAGjyR75bXaTNgItk,94827
scipy/special/tests/test_hypergeometric.py,sha256=qFhK0TMP2PkpFLBy8CpKq0fBqIJ8OUhxSjkxZ8rjs_Y,10230
scipy/special/tests/test_iv_ratio.py,sha256=r3ErUCvZqySpM0KlVJcfGBYzTYAPZNByyiOwdKskdOE,10357
scipy/special/tests/test_kolmogorov.py,sha256=WZQxJMHXgp5xC4JmrG-bXvEpzkA9pRXgxYTCAL2Xn_c,19771
scipy/special/tests/test_lambertw.py,sha256=AoYbanXWmx5twRL-HBARQx4RPSfHMPboSEXl0GXwNfQ,4669
scipy/special/tests/test_legendre.py,sha256=vN4ZuIFQHs5tXUDzgdv9I-r8dXk894RXgTflsVg5w8Q,59443
scipy/special/tests/test_log1mexp.py,sha256=-IqCTYkwzhIUF_ePjfdi9abiK_wntxw1D6j2I7DVXGU,3227
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=Fba-Ftq9bMw5YJ7pfE5yK2eERWaaWu4fuHt3lpNyGiM,6665
scipy/special/tests/test_logsumexp.py,sha256=cym_skxjfFHeQjbSr-zy0S8HJO-8rGF8Q27sxzWxI0o,19269
scipy/special/tests/test_mpmath.py,sha256=c3t_6e5OiZ-rVuZUWo3RmwpNtf5AS8StQ3oJBBCVdoQ,76082
scipy/special/tests/test_nan_inputs.py,sha256=8fpc_8jo4aiVVTV8FLItG9Vd0NsQoeJz0VWfPqhO0yE,1932
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=oqqx8DBRXpEvzUCYocyLvHHK6a2g7N8i18EdfveOxj4,32975
scipy/special/tests/test_orthogonal_eval.py,sha256=3NhYYdqkeNnma-BJgqGYGLFOW_Wlk628AgXksbJ0ugg,9846
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=4gZ_fHkWxMOjZ-ApUERxL2cqHvMcpMStvOv9wPSJAU0,529
scipy/special/tests/test_sf_error.py,sha256=5wy_9WUR3_bXfI-jzyxBg4K__Fqo0MLhb5jy6irY_QI,4386
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_specfun.py,sha256=wnuwcdU4BDRe4xU4S6Ib2_3rUevGsHJSeYvKV5XLOU4,1735
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=ArofuQVYCm6RevEx0gP2AJ1_6ARrf4Qs5jL3j0LEPKU,2058
scipy/special/tests/test_sph_harm.py,sha256=u0skq7BGAWMURNHZlzUi5akUR7DrEmsHkD6GyZ5S1SY,3087
scipy/special/tests/test_spherical_bessel.py,sha256=LZQ9RRNLItd2pem6DH8dThkPRPy1vDR8H-TRlC57X6g,15427
scipy/special/tests/test_support_alternative_backends.py,sha256=Tmsa0IAOgdz48amH3W74VDu68U8K_NHwxs3IJRwaRtc,9980
scipy/special/tests/test_trig.py,sha256=vJL6u-XkIOfMOAAqAUs_xfbcJT4CG3HwF6iZ3fMmgjI,2404
scipy/special/tests/test_ufunc_signatures.py,sha256=s5AFKayOq0iSQ6d8Cwre4AUZI-JyQhgx1SSw4PUJt4w,1884
scipy/special/tests/test_wright_bessel.py,sha256=87ivSP98hCu3rV26blmzvkJ09pCbxNXWk7iud4xFWTM,7899
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_zeta.py,sha256=rSSgTb3nhE2pf0PyhqHsc2EU1YxKmYgB3PmGn35i4xg,11850
scipy/stats/__init__.py,sha256=Jknb0kVrDhtfzBBrcsbF1vAPROiiO3I0jMkuO16bNTw,19416
scipy/stats/__pycache__/__init__.cpython-313.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-313.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-313.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-313.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-313.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-313.pyc,,
scipy/stats/__pycache__/_common.cpython-313.pyc,,
scipy/stats/__pycache__/_constants.cpython-313.pyc,,
scipy/stats/__pycache__/_continued_fraction.cpython-313.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-313.pyc,,
scipy/stats/__pycache__/_correlation.cpython-313.pyc,,
scipy/stats/__pycache__/_covariance.cpython-313.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-313.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-313.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-313.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-313.pyc,,
scipy/stats/__pycache__/_distribution_infrastructure.cpython-313.pyc,,
scipy/stats/__pycache__/_entropy.cpython-313.pyc,,
scipy/stats/__pycache__/_finite_differences.cpython-313.pyc,,
scipy/stats/__pycache__/_fit.cpython-313.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-313.pyc,,
scipy/stats/__pycache__/_kde.cpython-313.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-313.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-313.pyc,,
scipy/stats/__pycache__/_mgc.cpython-313.pyc,,
scipy/stats/__pycache__/_morestats.cpython-313.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-313.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-313.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-313.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-313.pyc,,
scipy/stats/__pycache__/_new_distributions.cpython-313.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-313.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-313.pyc,,
scipy/stats/__pycache__/_probability_distribution.cpython-313.pyc,,
scipy/stats/__pycache__/_qmc.cpython-313.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-313.pyc,,
scipy/stats/__pycache__/_quantile.cpython-313.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-313.pyc,,
scipy/stats/__pycache__/_resampling.cpython-313.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-313.pyc,,
scipy/stats/__pycache__/_sampling.cpython-313.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-313.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-313.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-313.pyc,,
scipy/stats/__pycache__/_survival.cpython-313.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-313.pyc,,
scipy/stats/__pycache__/_variation.cpython-313.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-313.pyc,,
scipy/stats/__pycache__/_wilcoxon.cpython-313.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-313.pyc,,
scipy/stats/__pycache__/contingency.cpython-313.pyc,,
scipy/stats/__pycache__/distributions.cpython-313.pyc,,
scipy/stats/__pycache__/kde.cpython-313.pyc,,
scipy/stats/__pycache__/morestats.cpython-313.pyc,,
scipy/stats/__pycache__/mstats.cpython-313.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-313.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-313.pyc,,
scipy/stats/__pycache__/mvn.cpython-313.pyc,,
scipy/stats/__pycache__/qmc.cpython-313.pyc,,
scipy/stats/__pycache__/sampling.cpython-313.pyc,,
scipy/stats/__pycache__/stats.cpython-313.pyc,,
scipy/stats/_ansari_swilk_statistics.cp313-win_amd64.dll.a,sha256=SlzKHuUCblzoLrAHijKgN3s45kadXjZva9rWF5dHA70,1752
scipy/stats/_ansari_swilk_statistics.cp313-win_amd64.pyd,sha256=BAUYQg9PUfHShohn-fCUXJOH2aGwo9BFhikdhQAo_Wg,123392
scipy/stats/_axis_nan_policy.py,sha256=GQpFy65qCVJ6XeeDJQ_s1Ml7SuF4yrL8919edMpRvuo,32074
scipy/stats/_biasedurn.cp313-win_amd64.dll.a,sha256=j11D2CAfzXWnwwn7Ihvc5ytsRVhB0dguQf3tKKFLIvM,1580
scipy/stats/_biasedurn.cp313-win_amd64.pyd,sha256=3nkUZ_QEMs_SikkYNB3ZXWE6zdQQhuYUa7vU5g13cT0,355840
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=kq_rKOZO_Uapc-4MtRasQxdoLOMzYw-bc9jv9eQrFyc,33497
scipy/stats/_binomtest.py,sha256=gXacpbMuFCBgpwG_1__1InZhMgZsPfDZ3FFETkPybQQ,13493
scipy/stats/_bws_test.py,sha256=h-6Ra_vazN0T6FA1kHZkG1AkEQ6JiRkFzJwVxD5ghS8,7239
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=X7Fo6g5foBhU1ldV3ym3DE8uloF050eZsSw_4MSw3ls,1044
scipy/stats/_continued_fraction.py,sha256=cX3xethsVDC53u1i-VwtUKT0JskdwUbAvDcVYs7BG-8,15895
scipy/stats/_continuous_distns.py,sha256=9Ptx-k6WyS27I-CwQiokA9EIjuF4nIhBNra2qVb12ls,418754
scipy/stats/_correlation.py,sha256=7_7B_KDgRV5eX9qmQPwjgDVKKBTgddGoX2PkvPWdnzs,8124
scipy/stats/_covariance.py,sha256=0-F5fR0CuWqNpqb2qPj5j_nM2yGh2buqifVyTAkY6R4,23296
scipy/stats/_crosstab.py,sha256=-qNk2ozojQ2Zd94JYZ5eRcB3kT-GjR26WCP6DbbGk4g,7555
scipy/stats/_discrete_distns.py,sha256=kQ3hzB4OyfvqBPjz3Pnr9-BKWFg7cm4qcjAbcwi2byY,67571
scipy/stats/_distn_infrastructure.py,sha256=u0yT0bSNbpfmCbgdPlkw-WLu2FAUpwpdoSvSRDiAJP0,156546
scipy/stats/_distr_params.py,sha256=zwtg0mBOTQxKLElc2wfKouUVHVws5kDgq-2qe1YUAg4,9351
scipy/stats/_distribution_infrastructure.py,sha256=RKfPaFA8L7vsb2hGqh8yi4vbPWwMjM5xoPUyOr6Le8I,239426
scipy/stats/_entropy.py,sha256=rTNIicbHEZyqhRIlllb48M1e8T1YHNg2zW9c92D9vws,16196
scipy/stats/_finite_differences.py,sha256=hA425eBSAQEcpk6E_s0FHzGR-VmJl4ewlDuJ3rdRyOg,4313
scipy/stats/_fit.py,sha256=b9q830FSqMvz-LCEG0vSkwXN4Scr6G6pnrSRksLvA5Q,61098
scipy/stats/_hypotests.py,sha256=bczNuqqRikasCGpazwyaafJsKeKyQGMM4GtwOT61kfs,83406
scipy/stats/_kde.py,sha256=mPS1xPK7KuJIdxWn3Lc4IQ_9rIHMLDvgFcluF8CNTw0,26410
scipy/stats/_ksstats.py,sha256=hNyCC-sfDpIM9jCJ5hXEavEn9aIEBI8b_H-jOZ3H9_k,20740
scipy/stats/_levy_stable/__init__.py,sha256=VFyyd7hMJwNl8BmFYDJl-FTNBu7iaVtwKI2oT90AYIY,47134
scipy/stats/_levy_stable/__pycache__/__init__.cpython-313.pyc,,
scipy/stats/_levy_stable/levyst.cp313-win_amd64.dll.a,sha256=6WhuibTBATWk4k5J0u90NZaOfGlwKP8fBpbfVO6MBho,1532
scipy/stats/_levy_stable/levyst.cp313-win_amd64.pyd,sha256=W5T11ZrJGRp9vk65pQbgkGDVIfp-MoKQEB37iAJWzuw,53248
scipy/stats/_mannwhitneyu.py,sha256=FqQBZYBT7vi-6iSHB8oDTzyqhSFrHFm6RuXT1Br47dI,19822
scipy/stats/_mgc.py,sha256=_yzzzEi9nPaFV6pZE1_epNRg5mYzssDRuF7F_DN_eQY,21909
scipy/stats/_morestats.py,sha256=_I8HKKfDXhzTmLBY-tkuQ374ShhQjwzSy8PHUhhtfTY,177071
scipy/stats/_mstats_basic.py,sha256=Z_qGN1nCteoNIuftB-IUHsrfyf_WUhc7IPY_VyZIxeI,126567
scipy/stats/_mstats_extras.py,sha256=GVNjjVGQaR2-duEoTPQmMd14M2HnrwNJCSQ8QTRuG1M,16883
scipy/stats/_multicomp.py,sha256=w0MZm7pJPB0rQYYtloKNS6bxPcGD7PbgTBtj7WFDVos,17285
scipy/stats/_multivariate.py,sha256=3Wg9D5qIkM91jVBiYn6n1yaIvST1LRJ0B-CVCNa9tK4,255905
scipy/stats/_new_distributions.py,sha256=XZSBOxxyV8Et6hyxbMoUlqB4BC41lIhJVK1PAVzx33A,16644
scipy/stats/_odds_ratio.py,sha256=KL3uWWfvfbnEwEkqRiUZJzOr_NtvAy09hvN3dVM8dZw,17471
scipy/stats/_page_trend_test.py,sha256=bXtWS42SNfJIM2BQfqUiyzMWC80RfxOsbwlii2fuows,19720
scipy/stats/_probability_distribution.py,sha256=vpPBsNCXERz0O051rEfqj6tIMmj_xTRkT1lxtk7hFwQ,71878
scipy/stats/_qmc.py,sha256=9CdX8RvOavrfqidfawtvl2MUUyPUh1_ZnCmKn1NWeYo,110763
scipy/stats/_qmc_cy.cp313-win_amd64.dll.a,sha256=h8pDVRlPHNAdzLHi3kCuDJRDUoiRotX4pLQwYXo2pT4,1544
scipy/stats/_qmc_cy.cp313-win_amd64.pyd,sha256=StSJ14ZoiotFgmRT4snKZqh6hWSdqpyZAUtXznr4J7s,295424
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=Qr2rb-5Z-GFFlayRr_ZspabLqME2W7-R0uEHwK0p7bQ,16172
scipy/stats/_qmvnt_cy.cp313-win_amd64.dll.a,sha256=rax_SG8VW4MExuoeG85nbBmlEY7OKm_ARGsmHy7VRe8,1568
scipy/stats/_qmvnt_cy.cp313-win_amd64.pyd,sha256=c6qoITfP7Gf1MMrPOOFkfoy3YUjl8-X4kUvkDhIqTrk,137216
scipy/stats/_quantile.py,sha256=a2aiB6zUdVz0qDFpti-r2qc1syEUw0OarvimbOT8VYw,13753
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/__pycache__/__init__.cpython-313.pyc,,
scipy/stats/_rcont/rcont.cp313-win_amd64.dll.a,sha256=9HNPCNwTwJJQnQ9jW6vbLJ30lQ1jV02p75jjDVHryj8,1520
scipy/stats/_rcont/rcont.cp313-win_amd64.pyd,sha256=UrpPnZt3JflExSiwLshLFQP6smCZkHq6VtaAYE_Nm6c,114176
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=JSkFTpQfP7c9nzm9ILd46h62hifSVmYysqZv3Di734U,105356
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_sampling.py,sha256=Hx_WSAZbxhUoOlE4vBINuqiV0zxcTH2mW6sT8FAQCZ4,47721
scipy/stats/_sensitivity_analysis.py,sha256=lTO1lFNHf0zpLA96VUXvaUmd53l2Qh-ymV96JyzqJGo,25754
scipy/stats/_sobol.cp313-win_amd64.dll.a,sha256=kc4heFYF6CdMpt4te-V_Y6AvpEj1n5MHr521vQ5Ro-8,1532
scipy/stats/_sobol.cp313-win_amd64.pyd,sha256=s2Jcy8xw1avX-NpVP8frJmLbqQtWavUA3UdGPNruq_o,232960
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cp313-win_amd64.dll.a,sha256=X05OFk1Iov-YpWqDOcR3p-xnCq2U7OT6M-Bii9l2UgM,1532
scipy/stats/_stats.cp313-win_amd64.pyd,sha256=rWIY8ptCf9Y05c3yjQ_hyPmWDaji7z1ZjmiwCEKJ3qM,541696
scipy/stats/_stats.pxd,sha256=EU9o8xwL148qbx8h0aI2Uy-lUtZ3i683HIRZRkTvjHM,719
scipy/stats/_stats_mstats_common.py,sha256=C46FC1_pb6NS-TRMOxZvGe1T8V4uTI2oQ1D2UVCeEaY,12794
scipy/stats/_stats_py.py,sha256=fihe-NQGySEsuYVu5ggFieuZW9KGSqjZB8lLFngldAM,433883
scipy/stats/_stats_pythran.cp313-win_amd64.dll.a,sha256=V2iBMZ6CQcJvLPL_afiJBAqG0Q7wWZ_YUuzBnrRrsAo,1628
scipy/stats/_stats_pythran.cp313-win_amd64.pyd,sha256=-fq62epIA7ynqyHhISX1B_mLYKxMw805EysmcbhX428,1086464
scipy/stats/_survival.py,sha256=t6IJKabL1FqmWKtx37Ve45SuviGjYVW5E6-WRQLlZwk,26622
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-313.pyc,,
scipy/stats/_unuran/unuran_wrapper.cp313-win_amd64.dll.a,sha256=ne5Fdb6jbT4CVPlSqD3w5A6RvtXMKvKURYE1vc1lCa4,1628
scipy/stats/_unuran/unuran_wrapper.cp313-win_amd64.pyd,sha256=g_IK8R7Jc4hZkcBTTQYtAcOKHElYG2hFZ39LjIL47oA,1291264
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=pznEl0yl5uiH9bMRaDdUDmP_BI8hv1vRa2hP-g_csoQ,5794
scipy/stats/_variation.py,sha256=sbsgVYIojdECd-BFMNp4ZvcMrLcFbvYs8gH2pZrURpA,4691
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/_wilcoxon.py,sha256=o5oPIwIk6Ib0gBKCoC9KqD6Ihiju_2WIgiHJvjN4ur0,9772
scipy/stats/biasedurn.py,sha256=gb1UJyRCcvPzqliBEPcPBYUPrIha8ztIOXphhQc6QG4,447
scipy/stats/contingency.py,sha256=tRU6tvs3pDP9WI9X1IrZqThUAnaFtfRWFmtPFD8x3zQ,19170
scipy/stats/distributions.py,sha256=_nRpDudL-PbnWvNm3fxRQ-00zEtCleSqhTNk1evZvq8,883
scipy/stats/kde.py,sha256=CtU4WVO1iuK6f80cn8Lz8z75OXwE0z4BevdRy_o_bP4,534
scipy/stats/morestats.py,sha256=2qX7gBOG7_3PqAtwLS0uM1198ehUm8z8u8W2pikxaYs,1000
scipy/stats/mstats.py,sha256=jzvELjOG5FulzAORL52iNL6VwQhA3N27RAr_-8fSu1Y,2606
scipy/stats/mstats_basic.py,sha256=4GSiBxQUMXnsBtFoV8BW6etxocpU69xfLzLFHO1gt9c,1436
scipy/stats/mstats_extras.py,sha256=OEMN7L4PfGARho8qIF9fmUg6TXnQcWBVWBHLeZ7m1l0,746
scipy/stats/mvn.py,sha256=WopFz0qBdEbGzhKk7eGfFVa9ojHkg2aKn3nzOjAkq4s,515
scipy/stats/qmc.py,sha256=tm9AkmRLZQCSc9HDwXywCd4b_lcxqQZbeRsNtqTWfEY,11939
scipy/stats/sampling.py,sha256=DB2ePAYSuu7z4FH2CcSSW9ifDWTQi8q1lTbpNitCxCY,2012
scipy/stats/stats.py,sha256=N2DCCNuXdZJ_EKuON0UCUF1gu_S8p20PEaJTCfoPfg8,1553
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-313.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_continued_fraction.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_continuous.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_correlation.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_marray.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_mgc.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_quantile.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-313.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-313.pyc,,
scipy/stats/tests/common_tests.py,sha256=v4LXkjqd7rCcGCLtQSN_4O8JIETktW12ivdGTQpDYro,12877
scipy/stats/tests/data/__pycache__/_mvt.cpython-313.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-313.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=FhbZqSVqMei58-awIxMScB_gSDj_1Fy-RKRkJwuBBns,7076
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=_U20YT6azRDMws9Bvf0Kp1YA4Q1UrJJIeAtpFhDXGSI,62094
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=ix_2PGZGRxzxVZEDbc_GsNhRaeNwk69Ntu6JkcCspjQ,11231
scipy/stats/tests/test_continued_fraction.py,sha256=fT2HvWtow_vyLNgWjyWcz0eYZ1ltHmFcLcR_watDQdw,6882
scipy/stats/tests/test_continuous.py,sha256=41rT2dTRQNbMhf51kUteHEEH2rQKQ5BwuZzG9c7PwcI,95366
scipy/stats/tests/test_continuous_basic.py,sha256=2OlG9jyqYbWwEtjtessOnMkTghWB-qQZgH-GZQMp7DM,44254
scipy/stats/tests/test_continuous_fit_censored.py,sha256=PtKzuvJPtFTF__pr209v-i34pcYOXSCB1vMfz8Uez3M,24871
scipy/stats/tests/test_correlation.py,sha256=ecJnDF84nPWHg43aEN6DMMsp46g8tQ5aeVz0ErUihLg,3587
scipy/stats/tests/test_crosstab.py,sha256=mfrrI2cyyW5XpSrFpGI7oqcSBfbzxVJkPCv1TOO9NoU,4021
scipy/stats/tests/test_discrete_basic.py,sha256=Rh5PaRKQDtn9pXWC3VgWaK8-arL1xmkQnTMKlhLF3b0,21919
scipy/stats/tests/test_discrete_distns.py,sha256=UDnpnv1M4stxPHfZ21YBi11vVuPc7ktxbRXYDfZl5Yc,26032
scipy/stats/tests/test_distributions.py,sha256=HnuEa7Tmq09WeemE-9Ow9QVg_0nD6pnnk1iF3vSQ2B0,423700
scipy/stats/tests/test_entropy.py,sha256=s8UwTasUUZx41WE2NeHB1BBUZublNw_i-V489Y0ImYc,13289
scipy/stats/tests/test_fast_gen_inversion.py,sha256=V3OJ8ppJ3OfkFd1ABkSiqZqalvIq9Sl_PKy4xJ3jGPU,16368
scipy/stats/tests/test_fit.py,sha256=oAn9_5m4fFy5LPjPwRZF-5MQBxo98p1Q8PpIMPkDzHc,50021
scipy/stats/tests/test_hypotests.py,sha256=sLXjwYD7L_U38QekuHRLthhyVJg1myhWQSiyM1xicTk,87231
scipy/stats/tests/test_kdeoth.py,sha256=jhXT1NjVDaGUZbh1EqRkXvw8xLayn2sIVbq7c017IYk,23499
scipy/stats/tests/test_marray.py,sha256=q0swya_jVZrOg6OigvPAaCWfbsjhi7tykjfzuytFflk,12743
scipy/stats/tests/test_mgc.py,sha256=_lAD5mNMBScEjkPpCJNMWrhZNClHwW3pHtFMR0YNu4o,8178
scipy/stats/tests/test_morestats.py,sha256=jaBGteQ12eCF8olFVgyiO30P_DmE9-RmrGgYbnMa6m0,146280
scipy/stats/tests/test_mstats_basic.py,sha256=yCGkRU-pocxOVlSIFAQab_Wo4rR-PJxkywD7YkkFgP8,89365
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=ILxEEqaRleWZz200whB8F9TH9wKB5hEmprOc9ACwhzs,18231
scipy/stats/tests/test_multivariate.py,sha256=RKusgTHb7uFAqRpN7LuUkF5qiiGN8kuwI3vpU9-EQKs,177682
scipy/stats/tests/test_odds_ratio.py,sha256=UtuPRXBu9QTZ-gMynkqB1XxqJs0A3ldUBPh0wmEcjww,6875
scipy/stats/tests/test_qmc.py,sha256=2fcnmpvg8cuMErp4Ykcql8TyIBicCJDU1ZMR_BdO8y8,59097
scipy/stats/tests/test_quantile.py,sha256=ZGAiWOT-eT__2Xmo1p3xDbwNldwzA_VAhoR3TPl5HnY,8711
scipy/stats/tests/test_rank.py,sha256=EPpzBW57Vb7ynWToU5db1TEsW4MVjyPyWOabpYhLpjQ,12993
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=l0HEoJoVKeJeN9tAsVHtCAF92GwW9oEshAjYDrO4v0w,84080
scipy/stats/tests/test_sampling.py,sha256=KmvdU-xxtVPADiRQdW1UqK0EOZmWYKmKFjkoSKmBd4w,56207
scipy/stats/tests/test_sensitivity_analysis.py,sha256=WxCMsbluDeFPFUvIKf7DudvtRVdMHSMmzymPIyZoBSs,10988
scipy/stats/tests/test_stats.py,sha256=D6RYd2EXNxQ93X-wkB0sxamHLznVYqnWKje7ijYWc5E,418330
scipy/stats/tests/test_survival.py,sha256=bO_oeXtEdpkMDEpralDhflpqVxiEOWwUnuqUcMQ1oTA,22424
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=qL-FvYR19e3VomVDuF2DkESUxkAlKlBHkNuUp3BOYcc,9609
scipy/version.py,sha256=hl9e49hvPSctz2HS0GVkADITEn2al3Vj2Do1_W9GeyM,330
